<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣APP - 科技感高交互设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            padding: 20px;
            min-height: 100vh;
            color: #ffffff;
            overflow-x: auto;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #00d4ff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: brightness(1); }
            to { filter: brightness(1.2); }
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-bottom: 40px;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 25px;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.5),
                inset 0 1px 0 rgba(255,255,255,0.1),
                0 0 20px rgba(0, 212, 255, 0.2);
        }

        .screen {
            width: 100%;
            height: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
        }

        .status-bar {
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            color: #00d4ff;
        }

        .header {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border-radius: 20px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            box-shadow: 
                inset 5px 5px 10px rgba(0,0,0,0.3),
                inset -5px -5px 10px rgba(255,255,255,0.05);
        }

        .header h1 {
            font-size: 18px;
            font-weight: 500;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .content {
            flex: 1;
            overflow-y: auto;
        }

        .neo-button {
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 8px 0;
            box-shadow: 
                5px 5px 10px rgba(0,0,0,0.3),
                -5px -5px 10px rgba(255,255,255,0.05);
            position: relative;
            overflow: hidden;
        }

        .neo-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .neo-button:hover::before {
            left: 100%;
        }

        .neo-button:active {
            box-shadow: 
                inset 3px 3px 6px rgba(0,0,0,0.3),
                inset -3px -3px 6px rgba(255,255,255,0.05);
            transform: scale(0.98);
        }

        .neo-card {
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 
                8px 8px 16px rgba(0,0,0,0.3),
                -8px -8px 16px rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
        }

        .neo-input {
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            color: #ffffff;
            width: 100%;
            margin: 8px 0;
            box-shadow: 
                inset 3px 3px 6px rgba(0,0,0,0.3),
                inset -3px -3px 6px rgba(255,255,255,0.05);
        }

        .neo-input::placeholder {
            color: #666;
        }

        .neo-input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 
                inset 3px 3px 6px rgba(0,0,0,0.3),
                inset -3px -3px 6px rgba(255,255,255,0.05),
                0 0 10px rgba(0, 212, 255, 0.3);
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #888;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            color: #00d4ff;
            transform: translateY(-2px);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border-radius: 50%;
            box-shadow: 
                3px 3px 6px rgba(0,0,0,0.3),
                -3px -3px 6px rgba(255,255,255,0.1),
                0 0 10px rgba(0, 212, 255, 0.3);
        }

        .ai-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00d4ff, #ff00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 16px;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.3),
                -6px -6px 12px rgba(255,255,255,0.1),
                0 0 20px rgba(0, 212, 255, 0.5);
            animation: pulse 2s infinite, rotate 10s linear infinite;
            position: relative;
            overflow: hidden;
        }

        .ai-button::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(transparent, rgba(255,255,255,0.3), transparent);
            animation: spin 3s linear infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .list-item {
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            padding: 15px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 
                4px 4px 8px rgba(0,0,0,0.3),
                -4px -4px 8px rgba(255,255,255,0.05);
        }

        .list-item:hover {
            transform: translateY(-2px);
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.4),
                -6px -6px 12px rgba(255,255,255,0.1),
                0 0 15px rgba(0, 212, 255, 0.2);
        }

        .list-item.completed {
            background: linear-gradient(145deg, #0d4f3c, #1a7a5e);
            border-color: rgba(0, 255, 128, 0.3);
            color: #00ff80;
        }

        .grid-layout {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }

        .grid-item {
            aspect-ratio: 1;
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            color: #ffffff;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 
                4px 4px 8px rgba(0,0,0,0.3),
                -4px -4px 8px rgba(255,255,255,0.05);
        }

        .grid-item:hover {
            transform: translateY(-3px);
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.4),
                -6px -6px 12px rgba(255,255,255,0.1),
                0 0 15px rgba(0, 212, 255, 0.3);
        }

        .stats-card {
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.3),
                -6px -6px 12px rgba(255,255,255,0.05);
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 
                8px 8px 16px rgba(0,0,0,0.4),
                -8px -8px 16px rgba(255,255,255,0.1),
                0 0 20px rgba(0, 212, 255, 0.2);
        }

        .stats-number {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(45deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 12px;
            color: #888;
        }

        .chart-container {
            height: 150px;
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.3),
                inset -4px -4px 8px rgba(255,255,255,0.05);
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            animation: scan 3s linear infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .landscape {
            transform: rotate(90deg);
            transform-origin: center;
            width: 812px;
            height: 375px;
            margin: 223px -223px;
        }

        .landscape .content {
            display: flex;
            flex-direction: row;
            gap: 20px;
        }

        .sidebar {
            width: 200px;
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.2),
                inset -4px -4px 8px rgba(255,255,255,0.05);
        }

        .main-grid {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 15px;
        }

        .right-panel {
            width: 200px;
            background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.2),
                inset -4px -4px 8px rgba(255,255,255,0.05);
        }

        /* 粒子效果背景 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00d4ff;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
            box-shadow: 0 0 6px #00d4ff;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }

        /* SVG图标定义 */
        .icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <!-- SVG图标系统 -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-warehouse" viewBox="0 0 24 24">
                <path d="M12 2L2 7v10c0 5.55 3.84 10 9 11 5.16-1 9-5.45 9-11V7l-10-5z"/>
            </symbol>
            <symbol id="icon-order" viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
            </symbol>
            <symbol id="icon-sort" viewBox="0 0 24 24">
                <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/>
            </symbol>
            <symbol id="icon-finance" viewBox="0 0 24 24">
                <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
            </symbol>
            <symbol id="icon-sell" viewBox="0 0 24 24">
                <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <h1 class="title">AI分拣APP - 科技感高交互设计</h1>
        
        <!-- 第一行：核心功能页面 -->
        <div class="screens-grid">
            <!-- 入库管理列表 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 10%; left: 20%; animation-delay: 0s;"></div>
                    <div class="particle" style="top: 30%; left: 80%; animation-delay: 1s;"></div>
                    <div class="particle" style="top: 60%; left: 40%; animation-delay: 2s;"></div>
                    <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>入库管理</h1>
                    </div>
                    <div class="content">
                        <button class="neo-button">新增入库单</button>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">入库单 #001</div>
                                <div style="font-size: 12px; color: #888;">2024-01-15</div>
                            </div>
                            <div style="color: #ff9500; font-weight: 600;">未入库</div>
                        </div>
                        
                        <div class="list-item completed">
                            <div>
                                <div style="font-weight: 600;">入库单 #002</div>
                                <div style="font-size: 12px; color: #00ff80;">2024-01-14</div>
                            </div>
                            <div style="font-weight: 600;">已入库</div>
                        </div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">入库单 #003</div>
                                <div style="font-size: 12px; color: #888;">2024-01-13</div>
                            </div>
                            <div style="color: #ff9500; font-weight: 600;">未入库</div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 入库详情 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 15%; left: 30%; animation-delay: 0.5s;"></div>
                    <div class="particle" style="top: 45%; left: 75%; animation-delay: 1.5s;"></div>
                    <div class="particle" style="top: 70%; left: 25%; animation-delay: 2.5s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>入库详情</h1>
                    </div>
                    <div class="content">
                        <div class="neo-card">
                            <div style="font-weight: 600; margin-bottom: 10px;">入库单 #001</div>
                            <div style="font-size: 14px; color: #888;">2024-01-15 09:30</div>
                        </div>

                        <div class="list-item completed">
                            <div>
                                <div style="font-weight: 600;">小白菜</div>
                                <div style="font-size: 12px;">5斤</div>
                            </div>
                            <div style="color: #00ff80;">✓</div>
                        </div>

                        <div class="list-item completed">
                            <div>
                                <div style="font-weight: 600;">大白菜</div>
                                <div style="font-size: 12px;">6斤</div>
                            </div>
                            <div style="color: #00ff80;">✓</div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">蘑菇</div>
                                <div style="font-size: 12px;">1箱</div>
                            </div>
                            <div style="color: #ff9500;">待入库</div>
                        </div>

                        <button class="neo-button" style="margin-top: 20px;">确认入库</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 录单功能 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 20%; left: 60%; animation-delay: 1s;"></div>
                    <div class="particle" style="top: 50%; left: 20%; animation-delay: 2s;"></div>
                    <div class="particle" style="top: 75%; left: 80%; animation-delay: 3s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>录单功能</h1>
                    </div>
                    <div class="content">
                        <input class="neo-input" placeholder="客户姓名" />
                        <input class="neo-input" placeholder="联系电话" />

                        <div style="margin: 20px 0; font-weight: 600; color: #00d4ff;">商品清单</div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">小白菜</div>
                                <div style="font-size: 12px; color: #888;">5斤 × ¥3.5</div>
                            </div>
                            <div style="font-weight: 600; color: #00d4ff;">¥17.5</div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">大白菜</div>
                                <div style="font-size: 12px; color: #888;">6斤 × ¥2.8</div>
                            </div>
                            <div style="font-weight: 600; color: #00d4ff;">¥16.8</div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">蘑菇</div>
                                <div style="font-size: 12px; color: #888;">1箱 × ¥25</div>
                            </div>
                            <div style="font-weight: 600; color: #00d4ff;">¥25</div>
                        </div>

                        <div class="neo-card" style="margin-top: 20px;">
                            <div style="display: flex; justify-content: space-between; font-weight: 600; font-size: 18px;">
                                <span>总计：</span>
                                <span style="background: linear-gradient(45deg, #00d4ff, #ff00ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">¥59.3</span>
                            </div>
                        </div>

                        <button class="neo-button">确认录单</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分拣系统（横屏） -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 25%; left: 40%; animation-delay: 0.8s;"></div>
                    <div class="particle" style="top: 55%; left: 85%; animation-delay: 1.8s;"></div>
                    <div class="particle" style="top: 85%; left: 15%; animation-delay: 2.8s;"></div>
                </div>
                <div class="screen landscape">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>分拣系统</h1>
                    </div>
                    <div class="content">
                        <div class="sidebar">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">分类</div>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">叶菜类</button>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">根茎类</button>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">菌菇类</button>
                        </div>
                        <div class="main-grid">
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(0, 255, 128, 0.5);"></div>
                                <span>小白菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #80ff80, #66cc66); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(128, 255, 128, 0.5);"></div>
                                <span>大白菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #40ff40, #33cc33); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(64, 255, 64, 0.5);"></div>
                                <span>菠菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #60ff60, #4dcc4d); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(96, 255, 96, 0.5);"></div>
                                <span>生菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #20ff20, #1acc1a); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(32, 255, 32, 0.5);"></div>
                                <span>韭菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ff8040, #cc6633); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(255, 128, 64, 0.5);"></div>
                                <span>胡萝卜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ffcc80, #cc9966); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(255, 204, 128, 0.5);"></div>
                                <span>土豆</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ffe080, #ccb366); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(255, 224, 128, 0.5);"></div>
                                <span>洋葱</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #d4af37, #b8941f); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);"></div>
                                <span>蘑菇</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #dda0dd, #b380b3); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(221, 160, 221, 0.5);"></div>
                                <span>香菇</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #98fb98, #7dc87d); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(152, 251, 152, 0.5);"></div>
                                <span>金针菇</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #87ceeb, #6ba6cd); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);"></div>
                                <span>平菇</span>
                            </div>
                        </div>
                        <div class="right-panel">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">已分拣</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(0, 255, 128, 0.2); border-radius: 8px; border: 1px solid rgba(0, 255, 128, 0.3);">小白菜 5斤 ✓</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(0, 255, 128, 0.2); border-radius: 8px; border: 1px solid rgba(0, 255, 128, 0.3);">大白菜 6斤 ✓</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(255, 149, 0, 0.2); border-radius: 8px; border: 1px solid rgba(255, 149, 0, 0.3);">蘑菇 1箱 ⏳</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行：财务管理、快速售卖、中控台、会员中心 -->
        <div class="screens-grid">
            <!-- 财务管理 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 12%; left: 25%; animation-delay: 0.3s;"></div>
                    <div class="particle" style="top: 35%; left: 70%; animation-delay: 1.3s;"></div>
                    <div class="particle" style="top: 65%; left: 45%; animation-delay: 2.3s;"></div>
                    <div class="particle" style="top: 85%; left: 80%; animation-delay: 3.3s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>财务管理</h1>
                    </div>
                    <div class="content">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="stats-card">
                                <div class="stats-number">¥12,580</div>
                                <div class="stats-label">今日收益</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">¥3,240</div>
                                <div class="stats-label">待收款</div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <svg width="300" height="120" viewBox="0 0 300 120">
                                <defs>
                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#ff00ff;stop-opacity:0.2" />
                                    </linearGradient>
                                </defs>
                                <path d="M20,100 L50,80 L80,60 L110,70 L140,40 L170,50 L200,30 L230,45 L260,25 L280,35"
                                      stroke="#00d4ff" stroke-width="3" fill="none"
                                      stroke-dasharray="5,5"
                                      style="animation: dash 2s linear infinite;">
                                    <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
                                </path>
                                <path d="M20,100 L50,80 L80,60 L110,70 L140,40 L170,50 L200,30 L230,45 L260,25 L280,35 L280,100 L20,100"
                                      fill="url(#gradient)"/>
                            </svg>
                        </div>

                        <div class="neo-card">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">收支明细</div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                                <span>蔬菜销售</span>
                                <span style="color: #00ff80; font-weight: 600;">+¥2,580</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                                <span>进货成本</span>
                                <span style="color: #ff4444; font-weight: 600;">-¥1,200</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                                <span>运输费用</span>
                                <span style="color: #ff4444; font-weight: 600;">-¥150</span>
                            </div>
                        </div>

                        <button class="neo-button">查看详细报表</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速售卖（横屏） -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 18%; left: 55%; animation-delay: 0.7s;"></div>
                    <div class="particle" style="top: 42%; left: 15%; animation-delay: 1.7s;"></div>
                    <div class="particle" style="top: 72%; left: 85%; animation-delay: 2.7s;"></div>
                </div>
                <div class="screen landscape">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>快速售卖</h1>
                    </div>
                    <div class="content">
                        <div class="sidebar">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">分类</div>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">叶菜类</button>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">根茎类</button>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">菌菇类</button>
                            <button class="neo-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">特价商品</button>
                        </div>
                        <div class="main-grid">
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(0, 255, 128, 0.5);"></div>
                                <span>小白菜</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥3.5/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #80ff80, #66cc66); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(128, 255, 128, 0.5);"></div>
                                <span>大白菜</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥2.8/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #40ff40, #33cc33); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(64, 255, 64, 0.5);"></div>
                                <span>菠菜</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥4.2/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #60ff60, #4dcc4d); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(96, 255, 96, 0.5);"></div>
                                <span>生菜</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥3.8/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #20ff20, #1acc1a); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(32, 255, 32, 0.5);"></div>
                                <span>韭菜</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥5.0/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ff8040, #cc6633); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(255, 128, 64, 0.5);"></div>
                                <span>胡萝卜</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥3.2/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ffcc80, #cc9966); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(255, 204, 128, 0.5);"></div>
                                <span>土豆</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥2.5/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ffe080, #ccb366); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(255, 224, 128, 0.5);"></div>
                                <span>洋葱</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥3.0/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #d4af37, #b8941f); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);"></div>
                                <span>蘑菇</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥25/箱</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #dda0dd, #b380b3); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(221, 160, 221, 0.5);"></div>
                                <span>香菇</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥8.5/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #98fb98, #7dc87d); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(152, 251, 152, 0.5);"></div>
                                <span>金针菇</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥6.0/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #87ceeb, #6ba6cd); border-radius: 50%; margin-bottom: 5px; box-shadow: 0 0 10px rgba(135, 206, 235, 0.5);"></div>
                                <span>平菇</span>
                                <span style="color: #00d4ff; font-weight: 600;">¥7.2/斤</span>
                            </div>
                        </div>
                        <div class="right-panel">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">购物车</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(0, 212, 255, 0.2); border-radius: 8px; border: 1px solid rgba(0, 212, 255, 0.3);">
                                <div style="display: flex; justify-content: space-between;">
                                    <span>小白菜 2斤</span>
                                    <span>¥7.0</span>
                                </div>
                            </div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(0, 212, 255, 0.2); border-radius: 8px; border: 1px solid rgba(0, 212, 255, 0.3);">
                                <div style="display: flex; justify-content: space-between;">
                                    <span>蘑菇 1箱</span>
                                    <span>¥25.0</span>
                                </div>
                            </div>
                            <div style="margin-top: 20px; padding: 10px; background: linear-gradient(45deg, #00d4ff, #ff00ff); color: white; border-radius: 10px; text-align: center; font-weight: 600; box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);">
                                总计: ¥32.0
                            </div>
                            <button class="neo-button" style="margin-top: 10px; font-size: 14px;">结算</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中控台 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 22%; left: 35%; animation-delay: 0.4s;"></div>
                    <div class="particle" style="top: 48%; left: 75%; animation-delay: 1.4s;"></div>
                    <div class="particle" style="top: 78%; left: 25%; animation-delay: 2.4s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>中控台</h1>
                    </div>
                    <div class="content">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="stats-card">
                                <div class="stats-number">156</div>
                                <div class="stats-label">今日订单</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">89</div>
                                <div class="stats-label">分拣完成</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="stats-card">
                                <div class="stats-number">23</div>
                                <div class="stats-label">分拣中</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">12</div>
                                <div class="stats-label">在线人数</div>
                            </div>
                        </div>

                        <div class="neo-card">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">实时状态</div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(0, 255, 128, 0.1); border-radius: 8px; border: 1px solid rgba(0, 255, 128, 0.3);">
                                <span>分拣员A</span>
                                <span style="color: #00ff80; font-weight: 600;">工作中</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(0, 255, 128, 0.1); border-radius: 8px; border: 1px solid rgba(0, 255, 128, 0.3);">
                                <span>分拣员B</span>
                                <span style="color: #00ff80; font-weight: 600;">工作中</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(255, 149, 0, 0.1); border-radius: 8px; border: 1px solid rgba(255, 149, 0, 0.3);">
                                <span>分拣员C</span>
                                <span style="color: #ff9500; font-weight: 600;">休息中</span>
                            </div>
                        </div>

                        <div class="chart-container">
                            <div style="text-align: center; color: #888;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px; color: #00d4ff;">效率统计</div>
                                <div style="width: 80px; height: 80px; border: 8px solid transparent; border-top: 8px solid #00d4ff; border-right: 8px solid #ff00ff; border-radius: 50%; margin: 0 auto; animation: spin 2s linear infinite; box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);"></div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会员中心 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 28%; left: 50%; animation-delay: 0.6s;"></div>
                    <div class="particle" style="top: 58%; left: 20%; animation-delay: 1.6s;"></div>
                    <div class="particle" style="top: 88%; left: 80%; animation-delay: 2.6s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>会员中心</h1>
                    </div>
                    <div class="content">
                        <div class="neo-card">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #00d4ff, #ff00ff); border-radius: 50%; margin-right: 15px; box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);"></div>
                                <div>
                                    <div style="font-weight: 600; font-size: 18px;">张三</div>
                                    <div style="color: #888; font-size: 14px;">高级会员</div>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 20px 0; font-weight: 600; color: #00d4ff;">功能模块</div>

                        <div class="grid-layout" style="grid-template-columns: repeat(3, 1fr);">
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #00d4ff, #0099cc); border-radius: 50%; margin-bottom: 8px; box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);"></div>
                                <span>入库管理</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ff00ff, #cc00cc); border-radius: 50%; margin-bottom: 8px; box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);"></div>
                                <span>录单功能</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ffff00, #cccc00); border-radius: 50%; margin-bottom: 8px; box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);"></div>
                                <span>分拣系统</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 50%; margin-bottom: 8px; box-shadow: 0 0 10px rgba(0, 255, 128, 0.5);"></div>
                                <span>财务管理</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #ff8040, #cc6633); border-radius: 50%; margin-bottom: 8px; box-shadow: 0 0 10px rgba(255, 128, 64, 0.5);"></div>
                                <span>快速售卖</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: linear-gradient(45deg, #8040ff, #6633cc); border-radius: 50%; margin-bottom: 8px; box-shadow: 0 0 10px rgba(128, 64, 255, 0.5);"></div>
                                <span>中控台</span>
                            </div>
                        </div>

                        <button class="neo-button">添加子账号</button>
                        <button class="neo-button">子账号管理</button>
                        <button class="neo-button">权限设置</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：添加子账号、子账号列表、权限编辑、AI语音助手 -->
        <div class="screens-grid">
            <!-- 添加子账号 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 15%; left: 40%; animation-delay: 0.2s;"></div>
                    <div class="particle" style="top: 40%; left: 80%; animation-delay: 1.2s;"></div>
                    <div class="particle" style="top: 70%; left: 30%; animation-delay: 2.2s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>添加子账号</h1>
                    </div>
                    <div class="content">
                        <input class="neo-input" placeholder="用户名" />
                        <input class="neo-input" placeholder="密码" type="password" />
                        <input class="neo-input" placeholder="确认密码" type="password" />
                        <input class="neo-input" placeholder="姓名" />
                        <input class="neo-input" placeholder="手机号" />

                        <div style="margin: 20px 0; font-weight: 600; color: #00d4ff;">权限分配</div>

                        <div class="neo-card">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>入库管理</span>
                                <div style="width: 40px; height: 20px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 10px; position: relative; cursor: pointer; box-shadow: 0 0 10px rgba(0, 255, 128, 0.3);">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); transition: all 0.3s ease;"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>录单功能</span>
                                <div style="width: 40px; height: 20px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 10px; position: relative; cursor: pointer; box-shadow: 0 0 10px rgba(0, 255, 128, 0.3);">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); transition: all 0.3s ease;"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>分拣系统</span>
                                <div style="width: 40px; height: 20px; background: #666; border-radius: 10px; position: relative; cursor: pointer;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); transition: all 0.3s ease;"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>财务管理</span>
                                <div style="width: 40px; height: 20px; background: #666; border-radius: 10px; position: relative; cursor: pointer;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); transition: all 0.3s ease;"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>快速售卖</span>
                                <div style="width: 40px; height: 20px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 10px; position: relative; cursor: pointer; box-shadow: 0 0 10px rgba(0, 255, 128, 0.3);">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); transition: all 0.3s ease;"></div>
                                </div>
                            </div>
                        </div>

                        <button class="neo-button">创建账号</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 子账号列表 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 25%; left: 65%; animation-delay: 0.9s;"></div>
                    <div class="particle" style="top: 55%; left: 25%; animation-delay: 1.9s;"></div>
                    <div class="particle" style="top: 85%; left: 75%; animation-delay: 2.9s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>子账号管理</h1>
                    </div>
                    <div class="content">
                        <button class="neo-button">添加新账号</button>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">李四</div>
                                <div style="font-size: 12px; color: #888;">分拣员 | 13800138001</div>
                                <div style="font-size: 12px; color: #00ff80;">在线</div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <div style="padding: 5px 10px; background: linear-gradient(45deg, #00d4ff, #0099cc); color: white; border-radius: 8px; font-size: 12px; cursor: pointer; box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);">编辑</div>
                                <div style="padding: 5px 10px; background: linear-gradient(45deg, #ff4444, #cc3333); color: white; border-radius: 8px; font-size: 12px; cursor: pointer; box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);">删除</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">王五</div>
                                <div style="font-size: 12px; color: #888;">录单员 | 13900139001</div>
                                <div style="font-size: 12px; color: #ff9500;">离线</div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <div style="padding: 5px 10px; background: linear-gradient(45deg, #00d4ff, #0099cc); color: white; border-radius: 8px; font-size: 12px; cursor: pointer; box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);">编辑</div>
                                <div style="padding: 5px 10px; background: linear-gradient(45deg, #ff4444, #cc3333); color: white; border-radius: 8px; font-size: 12px; cursor: pointer; box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);">删除</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">赵六</div>
                                <div style="font-size: 12px; color: #888;">售卖员 | 13700137001</div>
                                <div style="font-size: 12px; color: #00ff80;">在线</div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <div style="padding: 5px 10px; background: linear-gradient(45deg, #00d4ff, #0099cc); color: white; border-radius: 8px; font-size: 12px; cursor: pointer; box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);">编辑</div>
                                <div style="padding: 5px 10px; background: linear-gradient(45deg, #ff4444, #cc3333); color: white; border-radius: 8px; font-size: 12px; cursor: pointer; box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);">删除</div>
                            </div>
                        </div>

                        <div class="neo-card" style="margin-top: 20px;">
                            <div style="font-weight: 600; margin-bottom: 10px; color: #00d4ff;">权限说明</div>
                            <div style="font-size: 14px; color: #888; line-height: 1.5;">
                                • 分拣员：仅可使用分拣系统<br>
                                • 录单员：可使用录单功能和入库管理<br>
                                • 售卖员：可使用快速售卖功能<br>
                                • 管理员：拥有所有权限
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限编辑页面 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 30%; left: 45%; animation-delay: 1.1s;"></div>
                    <div class="particle" style="top: 60%; left: 85%; animation-delay: 2.1s;"></div>
                    <div class="particle" style="top: 90%; left: 35%; animation-delay: 3.1s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>权限编辑</h1>
                    </div>
                    <div class="content">
                        <div class="neo-card">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(45deg, #00d4ff, #ff00ff); border-radius: 50%; margin-right: 15px; box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);"></div>
                                <div>
                                    <div style="font-weight: 600; font-size: 16px;">李四</div>
                                    <div style="color: #888; font-size: 14px;">13800138001</div>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 20px 0; font-weight: 600; color: #00d4ff;">功能权限</div>

                        <div class="neo-card">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">入库管理</div>
                                    <div style="font-size: 12px; color: #888;">管理进货和入库操作</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 10px; position: relative; cursor: pointer; box-shadow: 0 0 10px rgba(0, 255, 128, 0.3);">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">录单功能</div>
                                    <div style="font-size: 12px; color: #888;">为客户录入订单信息</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #666; border-radius: 10px; position: relative; cursor: pointer;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">分拣系统</div>
                                    <div style="font-size: 12px; color: #888;">进行商品分拣操作</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: linear-gradient(45deg, #00ff80, #00cc66); border-radius: 10px; position: relative; cursor: pointer; box-shadow: 0 0 10px rgba(0, 255, 128, 0.3);">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">财务管理</div>
                                    <div style="font-size: 12px; color: #888;">查看财务数据和报表</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #666; border-radius: 10px; position: relative; cursor: pointer;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">快速售卖</div>
                                    <div style="font-size: 12px; color: #888;">现场销售商品</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #666; border-radius: 10px; position: relative; cursor: pointer;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                        </div>

                        <button class="neo-button">保存权限</button>
                        <button class="neo-button" style="background: linear-gradient(45deg, #ff4444, #cc3333); border-color: rgba(255, 68, 68, 0.3);">重置密码</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI语音助手页面 -->
            <div class="phone-frame">
                <div class="particles">
                    <div class="particle" style="top: 20%; left: 60%; animation-delay: 0.5s;"></div>
                    <div class="particle" style="top: 50%; left: 20%; animation-delay: 1.5s;"></div>
                    <div class="particle" style="top: 80%; left: 70%; animation-delay: 2.5s;"></div>
                    <div class="particle" style="top: 35%; left: 90%; animation-delay: 3.5s;"></div>
                </div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>AI语音助手</h1>
                    </div>
                    <div class="content">
                        <div style="text-align: center; margin: 40px 0;">
                            <div style="width: 150px; height: 150px; background: linear-gradient(45deg, #00d4ff, #ff00ff, #ffff00); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; box-shadow: 0 20px 40px rgba(0,0,0,0.3), 0 0 30px rgba(0, 212, 255, 0.5); animation: pulse 2s infinite, rotate 10s linear infinite; position: relative; overflow: hidden;">
                                <div style="color: white; font-size: 48px; font-weight: 700; z-index: 2;">AI</div>
                                <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: conic-gradient(transparent, rgba(255,255,255,0.3), transparent); animation: spin 3s linear infinite;"></div>
                            </div>
                            <div style="margin-top: 20px; font-size: 18px; font-weight: 600; background: linear-gradient(45deg, #00d4ff, #ff00ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">点击开始对话</div>
                        </div>

                        <div class="neo-card">
                            <div style="font-weight: 600; margin-bottom: 15px; color: #00d4ff;">语音指令示例</div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(0, 212, 255, 0.1); border-radius: 8px; font-size: 14px; border: 1px solid rgba(0, 212, 255, 0.2);">
                                "帮我查看今天的订单数量"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(255, 0, 255, 0.1); border-radius: 8px; font-size: 14px; border: 1px solid rgba(255, 0, 255, 0.2);">
                                "录入一个新订单"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(255, 255, 0, 0.1); border-radius: 8px; font-size: 14px; border: 1px solid rgba(255, 255, 0, 0.2);">
                                "查看分拣进度"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(0, 255, 128, 0.1); border-radius: 8px; font-size: 14px; border: 1px solid rgba(0, 255, 128, 0.2);">
                                "今日财务报表"
                            </div>
                        </div>

                        <div class="neo-card">
                            <div style="font-weight: 600; margin-bottom: 10px; color: #00d4ff;">对话记录</div>
                            <div style="font-size: 14px; color: #888; line-height: 1.5;">
                                <div style="margin: 8px 0;">
                                    <span style="color: #00d4ff; font-weight: 600;">用户：</span>今天有多少订单？
                                </div>
                                <div style="margin: 8px 0;">
                                    <span style="color: #00ff80; font-weight: 600;">AI：</span>今天共有156个订单，其中89个已完成分拣。
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px; margin-top: 20px;">
                            <button class="neo-button" style="flex: 1;">清除记录</button>
                            <button class="neo-button" style="flex: 1;">设置</button>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
