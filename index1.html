<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能分拣系统 - 完整版UI设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: auto;
            padding: 20px;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 20px;
            max-width: 2400px;
            margin: 0 auto;
            justify-items: center;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #333;
            border-radius: 40px;
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .phone-frame.landscape-page {
            width: 812px;
            height: 375px;
            grid-column: span 2;
        }

        .screen {
            width: 100%;
            height: 100%;
            border-radius: 35px;
            overflow: hidden;
            position: relative;
        }

        .page {
            width: 100%;
            height: 100%;
            padding: 20px;
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            position: relative;
        }

        /* 玻璃拟态效果 */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }

        /* 发光按钮 */
        .glow-btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }

        .glow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 25px rgba(0, 212, 255, 0.5);
        }

        /* 标题样式 */
        .page-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(45deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
        }

        /* 输入框样式 */
        .input-field {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            margin-bottom: 16px;
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* 状态指示器 */
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-pending { background: #ff9500; }
        .status-processing { background: #00d4ff; }
        .status-completed { background: #30d158; }
        .status-error { background: #ff3b30; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 图标样式 */
        .icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        /* 导航栏 */
        .nav-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #00d4ff;
        }

        /* 粒子效果背景 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00d4ff;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 悬浮语音按钮 */
        .voice-btn {
            position: absolute;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff0080, #ff4081);
            border: none;
            box-shadow: 0 4px 20px rgba(255, 0, 128, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            animation: voicePulse 2s infinite;
        }

        @keyframes voicePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* 表格样式 */
        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .order-table th,
        .order-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .order-table th {
            background: rgba(0, 212, 255, 0.2);
            font-weight: 600;
            color: #00d4ff;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 统计卡片 */
        .stat-card {
            text-align: center;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        /* 横屏分拣页面 */
        .landscape-page {
            display: flex;
        }

        .sidebar {
            width: 100px;
            background: rgba(0, 0, 0, 0.8);
            padding: 12px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sidebar-item {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .sidebar-item.active {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
        }

        .main-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .product-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
        }

        .product-card.completed {
            border-color: #30d158;
            background: rgba(48, 209, 88, 0.1);
        }
    </style>
</head>
<body>
    <!-- SVG图标定义 -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-login" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17C12.8 17 13.5 16.3 13.5 15.5S12.8 14 12 14 10.5 14.7 10.5 15.5 11.2 17 12 17Z"/>
            </symbol>
            <symbol id="icon-scan" viewBox="0 0 24 24">
                <path d="M9,2V5H7V4A1,1 0 0,0 6,3H3A1,1 0 0,0 2,4V7A1,1 0 0,0 3,8H4V6H7V9H2V15H7V12H9V15A1,1 0 0,0 10,16H13A1,1 0 0,0 14,15V12H17V15H20V9H15V12H13V9A1,1 0 0,0 12,8H9A1,1 0 0,0 8,9V12H6V6H9V2M15,2V5H17V4A1,1 0 0,1 18,3H21A1,1 0 0,1 22,4V7A1,1 0 0,1 21,8H20V6H17V9H22V15H17V12H15V15A1,1 0 0,1 14,16H11A1,1 0 0,1 10,15V12H8V15H5V9H10V12H12V9A1,1 0 0,1 13,8H16A1,1 0 0,1 17,9V12H19V6H16V2H15Z"/>
            </symbol>
            <symbol id="icon-order" viewBox="0 0 24 24">
                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
            </symbol>
            <symbol id="icon-sort" viewBox="0 0 24 24">
                <path d="M9,3L5,7H8V14H10V7H13M16,17V10H14V17H11L15,21L19,17H16Z"/>
            </symbol>
            <symbol id="icon-profile" viewBox="0 0 24 24">
                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
            </symbol>
            <symbol id="icon-analytics" viewBox="0 0 24 24">
                <path d="M22,21H2V3H4V19H6V17H10V19H12V16H16V19H18V17H22V21Z"/>
            </symbol>
            <symbol id="icon-inventory" viewBox="0 0 24 24">
                <path d="M12,2L13.09,8.26L22,9L17,14L18.18,23L12,19.77L5.82,23L7,14L2,9L10.91,8.26L12,2Z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
            </symbol>
            <symbol id="icon-notification" viewBox="0 0 24 24">
                <path d="M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29C10,4.19 10,4.1 10,4A2,2 0 0,1 12,2A2,2 0 0,1 14,4C14,4.1 14,4.19 14,4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21"/>
            </symbol>
            <symbol id="icon-camera" viewBox="0 0 24 24">
                <path d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <!-- 第一行：登录页、主控台、入库模块、订单录入、分拣任务、个人中心 -->

        <!-- 登录页 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div class="particles">
                        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                        <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
                        <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
                        <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
                        <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
                    </div>

                    <div style="text-align: center; margin-top: 100px;">
                        <svg class="icon" style="width: 80px; height: 80px; margin-bottom: 30px;">
                            <use href="#icon-login"></use>
                        </svg>
                        <h1 class="page-title">AI智能分拣系统</h1>
                        <p style="color: rgba(255,255,255,0.7); margin-bottom: 40px;">科技赋能，智慧分拣</p>
                    </div>

                    <div class="glass" style="padding: 30px; margin: 40px 0;">
                        <input type="text" class="input-field" placeholder="请输入会员账号">
                        <input type="password" class="input-field" placeholder="请输入密码">

                        <button class="glow-btn" style="width: 100%; margin-top: 20px;">
                            登录系统
                        </button>

                        <div style="text-align: center; margin-top: 20px;">
                            <a href="#" style="color: #00d4ff; text-decoration: none;">忘记密码？</a>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 40px;">
                        <p style="color: rgba(255,255,255,0.5); font-size: 12px;">
                            权限验证 • 模块检测 • 安全登录
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主控台 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">智能主控台</h2>

                    <!-- 实时统计 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">今日概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #30d158;">156</div>
                                <div class="stat-label">已完成订单</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff9500;">23</div>
                                <div class="stat-label">待分拣订单</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #00d4ff;">8</div>
                                <div class="stat-label">在线员工</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff0080;">98.5%</div>
                                <div class="stat-label">系统效率</div>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px;">快捷操作</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <button class="glow-btn" style="padding: 16px; font-size: 14px;">
                                <svg class="icon" style="margin-bottom: 8px;"><use href="#icon-scan"></use></svg>
                                <div>扫码入库</div>
                            </button>
                            <button class="glow-btn" style="padding: 16px; font-size: 14px; background: linear-gradient(45deg, #30d158, #28a745);">
                                <svg class="icon" style="margin-bottom: 8px;"><use href="#icon-order"></use></svg>
                                <div>新建订单</div>
                            </button>
                            <button class="glow-btn" style="padding: 16px; font-size: 14px; background: linear-gradient(45deg, #ff9500, #ff6b00);">
                                <svg class="icon" style="margin-bottom: 8px;"><use href="#icon-sort"></use></svg>
                                <div>开始分拣</div>
                            </button>
                            <button class="glow-btn" style="padding: 16px; font-size: 14px; background: linear-gradient(45deg, #ff0080, #ff4081);">
                                <svg class="icon" style="margin-bottom: 8px;"><use href="#icon-analytics"></use></svg>
                                <div>数据分析</div>
                            </button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 入库模块 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">商品入库</h2>

                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">未入库</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">已入库</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024001</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 新鲜蔬菜批发</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 8种 | 预计到货: 今日14:00
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始入库</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024002</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 优质肉类配送</div>
                            </div>
                            <span class="status-dot status-processing"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 5种 | 预计到货: 今日16:30
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">继续入库</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024003</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 海鲜直供</div>
                            </div>
                            <span class="status-dot status-completed"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 3种 | 已完成入库
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px; background: linear-gradient(45deg, #30d158, #28a745);">查看详情</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单录入 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">订单录入</h2>

                    <div class="card" style="margin-bottom: 16px;">
                        <input type="text" class="input-field" placeholder="客户姓名" style="margin-bottom: 8px;">
                        <input type="text" class="input-field" placeholder="联系电话" style="margin-bottom: 8px;">
                        <input type="text" class="input-field" placeholder="配送地址" style="margin-bottom: 0;">
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3>商品清单</h3>
                            <div style="display: flex; gap: 8px;">
                                <button class="glow-btn" style="padding: 6px 12px; font-size: 12px; background: linear-gradient(45deg, #ff9500, #ff6b00);">添加商品</button>
                                <button class="glow-btn" style="padding: 6px 12px; font-size: 12px; background: linear-gradient(45deg, #30d158, #28a745);">确认订单</button>
                            </div>
                        </div>
                        <table class="order-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>数量</th>
                                    <th>单位</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>大白菜</td>
                                    <td>5</td>
                                    <td>斤</td>
                                    <td><span style="color: #ff3b30; cursor: pointer;">删除</span></td>
                                </tr>
                                <tr>
                                    <td>小白菜</td>
                                    <td>6</td>
                                    <td>斤</td>
                                    <td><span style="color: #ff3b30; cursor: pointer;">删除</span></td>
                                </tr>
                                <tr>
                                    <td>蘑菇</td>
                                    <td>1</td>
                                    <td>箱</td>
                                    <td><span style="color: #ff3b30; cursor: pointer;">删除</span></td>
                                </tr>
                                <tr>
                                    <td>西红柿</td>
                                    <td>3</td>
                                    <td>斤</td>
                                    <td><span style="color: #ff3b30; cursor: pointer;">删除</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 悬浮语音按钮 -->
                    <button class="voice-btn">
                        <svg style="width: 24px; height: 24px; fill: white;" viewBox="0 0 24 24">
                            <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
                        </svg>
                    </button>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分拣任务 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">分拣任务</h2>

                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">未分拣</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">进行中</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">已完成</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">张先生</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024001</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span class="status-dot status-pending"></span>
                                <span style="color: #ff9500; font-size: 12px; background: rgba(255,149,0,0.2); padding: 2px 8px; border-radius: 10px;">高优先级</span>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 3件 | 预计用时: 8分钟 | 配送时间: 15:30
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">李女士</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024002</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span class="status-dot status-pending"></span>
                                <span style="color: #00d4ff; font-size: 12px; background: rgba(0,212,255,0.2); padding: 2px 8px; border-radius: 10px;">中优先级</span>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 5件 | 预计用时: 12分钟 | 配送时间: 16:00
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">王先生</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024003</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span class="status-dot status-pending"></span>
                                <span style="color: rgba(255,255,255,0.6); font-size: 12px; background: rgba(255,255,255,0.1); padding: 2px 8px; border-radius: 10px;">低优先级</span>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 2件 | 预计用时: 5分钟 | 配送时间: 17:00
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-sort"></use></svg>
                            <span>分拣</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人中心 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(45deg, #00d4ff, #ff0080); margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                            <svg class="icon" style="width: 40px; height: 40px;">
                                <use href="#icon-profile"></use>
                            </svg>
                        </div>
                        <h2 style="margin-bottom: 8px;">张三</h2>
                        <p style="color: rgba(255,255,255,0.6); font-size: 14px;">高级分拣员 | ID: 001</p>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px;">本月统计</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; text-align: center;">
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #00d4ff;">156</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">完成订单</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #30d158;">98.5%</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">准确率</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #ff9500;">12.3</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">平均用时(分)</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #ff0080;">A+</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">绩效等级</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px;">系统设置</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>语音提示</span>
                            <div style="width: 40px; height: 20px; background: #00d4ff; border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>震动反馈</span>
                            <div style="width: 40px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                            </div>
                        </div>
                        <button class="glow-btn" style="width: 100%; background: linear-gradient(45deg, #ff0080, #ff4081);">
                            退出登录
                        </button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行：入库详情、分拣详情、库存管理、数据分析、通知中心、设置页面 -->

        <!-- 入库详情页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">入库详情</h2>

                    <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024001</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 新鲜蔬菜批发</div>
                            </div>
                            <span style="color: #00d4ff; font-size: 12px;">进行中</span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            到货时间: 2024-07-28 14:00 | 操作员: 张三
                        </div>
                        <div class="progress-bar" style="margin-top: 12px;">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 8px;">
                            进度: 6/8 商品已入库 (75%)
                        </div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3>商品清单</h3>
                            <div style="display: flex; gap: 8px;">
                                <button class="glow-btn" style="padding: 6px 12px; font-size: 12px; background: linear-gradient(45deg, #00d4ff, #0099cc);">扫码入库</button>
                                <button class="glow-btn" style="padding: 6px 12px; font-size: 12px; background: linear-gradient(45deg, #30d158, #28a745);">完成入库</button>
                            </div>
                        </div>
                        <table class="order-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>预计</th>
                                    <th>实际</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>大白菜</td>
                                    <td>50斤</td>
                                    <td style="color: #30d158;">50斤</td>
                                    <td><span style="color: #30d158; font-size: 12px;">✓</span></td>
                                </tr>
                                <tr>
                                    <td>小白菜</td>
                                    <td>30斤</td>
                                    <td style="color: #30d158;">30斤</td>
                                    <td><span style="color: #30d158; font-size: 12px;">✓</span></td>
                                </tr>
                                <tr>
                                    <td>西红柿</td>
                                    <td>25斤</td>
                                    <td style="color: #00d4ff;">20斤</td>
                                    <td><span style="color: #ff9500; font-size: 12px;">进行中</span></td>
                                </tr>
                                <tr>
                                    <td>黄瓜</td>
                                    <td>20斤</td>
                                    <td style="color: rgba(255,255,255,0.4);">-</td>
                                    <td><span style="color: rgba(255,255,255,0.4); font-size: 12px;">待入库</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 悬浮语音按钮 -->
                    <button class="voice-btn">
                        <svg style="width: 24px; height: 24px; fill: white;" viewBox="0 0 24 24">
                            <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
                        </svg>
                    </button>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分拣详情页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">分拣详情</h2>

                    <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">张先生订单</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单号: #SO2024001</div>
                            </div>
                            <span style="color: #00d4ff; font-size: 12px;">分拣中</span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            开始时间: 15:20 | 分拣员: 李四 | 预计完成: 15:28
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%;"></div>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 8px;">
                            进度: 2/3 商品已分拣 (60%)
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px;">分拣清单</h3>
                        <div style="display: grid; gap: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">蘑菇</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">1箱 | 货架A-3</div>
                                </div>
                                <div style="color: #30d158; font-size: 20px;">✓</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">西红柿</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">3斤 | 货架B-1</div>
                                </div>
                                <div style="color: #30d158; font-size: 20px;">✓</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(0,212,255,0.1); border: 1px solid #00d4ff; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">黄瓜</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">4斤 | 货架B-2</div>
                                </div>
                                <div style="color: #00d4ff; font-size: 16px;">进行中...</div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 8px; margin-top: 16px;">
                            <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #ff9500, #ff6b00);">暂停分拣</button>
                            <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #30d158, #28a745);">完成分拣</button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-sort"></use></svg>
                            <span>分拣</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库存管理 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">库存管理</h2>

                    <!-- 库存概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">库存概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #30d158;">85%</div>
                                <div class="stat-label">蔬菜类库存</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff9500;">45%</div>
                                <div class="stat-label">肉类库存</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff3b30;">20%</div>
                                <div class="stat-label">海鲜类库存</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #00d4ff;">156</div>
                                <div class="stat-label">商品种类</div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存预警 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff3b30;">库存预警</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,59,48,0.1); border: 1px solid #ff3b30; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff3b30;">海鲜类</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">库存严重不足</div>
                                </div>
                                <div style="color: #ff3b30; font-weight: 600;">20%</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff9500;">肉类</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">库存偏低</div>
                                </div>
                                <div style="color: #ff9500; font-weight: 600;">45%</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff9500;">调料类</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">需要补货</div>
                                </div>
                                <div style="color: #ff9500; font-weight: 600;">35%</div>
                            </div>
                        </div>

                        <button class="glow-btn" style="width: 100%; margin-top: 16px; background: linear-gradient(45deg, #ff9500, #ff6b00);">
                            生成采购计划
                        </button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-inventory"></use></svg>
                            <span>库存</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据分析 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">数据分析</h2>

                    <!-- 今日数据 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">今日数据</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #30d158;">¥12,580</div>
                                <div class="stat-label">销售额</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #00d4ff;">156</div>
                                <div class="stat-label">订单数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff9500;">8.5分</div>
                                <div class="stat-label">平均分拣时间</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff0080;">98.2%</div>
                                <div class="stat-label">准确率</div>
                            </div>
                        </div>
                    </div>

                    <!-- 趋势分析 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">本周趋势</h3>
                        <div style="height: 120px; background: rgba(255,255,255,0.05); border-radius: 8px; position: relative; overflow: hidden;">
                            <!-- 简化的图表背景 -->
                            <div style="position: absolute; bottom: 0; left: 10%; width: 8%; height: 60%; background: linear-gradient(to top, #00d4ff, rgba(0,212,255,0.3)); border-radius: 2px 2px 0 0;"></div>
                            <div style="position: absolute; bottom: 0; left: 25%; width: 8%; height: 80%; background: linear-gradient(to top, #00d4ff, rgba(0,212,255,0.3)); border-radius: 2px 2px 0 0;"></div>
                            <div style="position: absolute; bottom: 0; left: 40%; width: 8%; height: 45%; background: linear-gradient(to top, #00d4ff, rgba(0,212,255,0.3)); border-radius: 2px 2px 0 0;"></div>
                            <div style="position: absolute; bottom: 0; left: 55%; width: 8%; height: 90%; background: linear-gradient(to top, #00d4ff, rgba(0,212,255,0.3)); border-radius: 2px 2px 0 0;"></div>
                            <div style="position: absolute; bottom: 0; left: 70%; width: 8%; height: 70%; background: linear-gradient(to top, #00d4ff, rgba(0,212,255,0.3)); border-radius: 2px 2px 0 0;"></div>
                            <div style="position: absolute; bottom: 0; left: 85%; width: 8%; height: 85%; background: linear-gradient(to top, #00d4ff, rgba(0,212,255,0.3)); border-radius: 2px 2px 0 0;"></div>

                            <div style="position: absolute; bottom: 8px; left: 50%; transform: translateX(-50%); color: rgba(255,255,255,0.6); font-size: 12px;">
                                订单量趋势 (本周)
                            </div>
                        </div>
                    </div>

                    <!-- 效率排行 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">员工效率排行</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,215,0,0.1); border: 1px solid #ffd700; border-radius: 6px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="color: #ffd700; font-weight: 700;">🥇</div>
                                    <div>
                                        <div style="font-weight: 600;">张三</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">156单 | 98.5%准确率</div>
                                    </div>
                                </div>
                                <div style="color: #ffd700; font-weight: 600;">A+</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(192,192,192,0.1); border: 1px solid #c0c0c0; border-radius: 6px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="color: #c0c0c0; font-weight: 700;">🥈</div>
                                    <div>
                                        <div style="font-weight: 600;">李四</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">142单 | 97.8%准确率</div>
                                    </div>
                                </div>
                                <div style="color: #c0c0c0; font-weight: 600;">A</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(205,127,50,0.1); border: 1px solid #cd7f32; border-radius: 6px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="color: #cd7f32; font-weight: 700;">🥉</div>
                                    <div>
                                        <div style="font-weight: 600;">王五</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">128单 | 96.2%准确率</div>
                                    </div>
                                </div>
                                <div style="color: #cd7f32; font-weight: 600;">A-</div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>分析</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通知中心 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">通知中心</h2>

                    <!-- 未读通知 -->
                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">未读 (5)</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">已读</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">系统</button>
                    </div>

                    <!-- 通知列表 -->
                    <div class="card">
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding: 12px; background: rgba(255,59,48,0.1); border: 1px solid #ff3b30; border-radius: 8px;">
                            <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #ff3b30;">紧急库存预警</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 4px;">海鲜类库存严重不足，请及时补货</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.4); margin-top: 4px;">2分钟前</div>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding: 12px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 8px;">
                            <div style="width: 8px; height: 8px; background: #ff9500; border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #ff9500;">新订单提醒</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 4px;">收到5个新订单，请及时处理</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.4); margin-top: 4px;">5分钟前</div>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding: 12px; background: rgba(0,212,255,0.1); border: 1px solid #00d4ff; border-radius: 8px;">
                            <div style="width: 8px; height: 8px; background: #00d4ff; border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #00d4ff;">系统更新</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 4px;">分拣系统已更新至v2.1.0版本</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.4); margin-top: 4px;">1小时前</div>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                            <div style="width: 8px; height: 8px; background: #30d158; border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #30d158;">分拣完成</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 4px;">订单#SO2024001已完成分拣</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.4); margin-top: 4px;">2小时前</div>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 8px;">
                            <div style="width: 8px; height: 8px; background: #ff0080; border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #ff0080;">绩效提醒</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 4px;">恭喜！本月绩效达到A+等级</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.4); margin-top: 4px;">3小时前</div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="glow-btn" style="background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));">
                            标记全部已读
                        </button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-notification"></use></svg>
                            <span>通知</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">系统设置</h2>

                    <!-- 个人设置 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">个人设置</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>语音提示</span>
                            <div style="width: 40px; height: 20px; background: #00d4ff; border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>震动反馈</span>
                            <div style="width: 40px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>自动分拣提醒</span>
                            <div style="width: 40px; height: 20px; background: #30d158; border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>夜间模式</span>
                            <div style="width: 40px; height: 20px; background: #00d4ff; border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">系统设置</h3>
                        <div style="display: grid; gap: 12px;">
                            <button style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; color: white; cursor: pointer;">
                                <span>数据同步</span>
                                <span style="color: #00d4ff;">></span>
                            </button>
                            <button style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; color: white; cursor: pointer;">
                                <span>缓存清理</span>
                                <span style="color: #00d4ff;">></span>
                            </button>
                            <button style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; color: white; cursor: pointer;">
                                <span>版本信息</span>
                                <span style="color: rgba(255,255,255,0.6); font-size: 12px;">v2.1.0</span>
                            </button>
                            <button style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; color: white; cursor: pointer;">
                                <span>帮助与反馈</span>
                                <span style="color: #00d4ff;">></span>
                            </button>
                        </div>
                    </div>

                    <!-- 账户操作 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">账户操作</h3>
                        <div style="display: grid; gap: 12px;">
                            <button class="glow-btn" style="background: linear-gradient(45deg, #ff9500, #ff6b00);">
                                修改密码
                            </button>
                            <button class="glow-btn" style="background: linear-gradient(45deg, #ff0080, #ff4081);">
                                退出登录
                            </button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：横屏分拣页面、扫码页面、相机页面、分拣大厅、管理员页面、财务页面 -->

        <!-- 横屏分拣页面 -->
        <div class="phone-frame landscape-page">
            <div class="sidebar">
                <div class="sidebar-item active">分拣中</div>
                <div class="sidebar-item">已完成</div>
                <div class="sidebar-item">挂单</div>
                <div class="sidebar-item">暂停</div>
                <div class="sidebar-item">大厅</div>
            </div>
            <div class="main-content">
                <!-- 标题栏带按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <button class="glow-btn" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(45deg, #ff9500, #ff6b00);">挂单</button>
                    <h2 class="page-title" style="margin: 0;">分拣作业 - 张先生订单 #SO2024001</h2>
                    <button class="glow-btn" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(45deg, #30d158, #28a745);">分拣完成</button>
                </div>

                <div style="display: flex; gap: 16px; height: calc(100% - 80px);">
                    <!-- 左侧订单信息 -->
                    <div style="width: 200px;">
                        <div class="card" style="margin-bottom: 12px; padding: 12px;">
                            <h4 style="margin-bottom: 8px; color: #00d4ff;">订单信息</h4>
                            <div style="font-size: 12px; color: rgba(255,255,255,0.8); line-height: 1.4;">
                                <div>客户: 张先生</div>
                                <div>电话: 138****1234</div>
                                <div>地址: 北京市朝阳区xxx街道</div>
                                <div>配送时间: 15:30</div>
                                <div>优先级: 高</div>
                            </div>
                        </div>
                        <div class="card" style="padding: 12px;">
                            <h4 style="margin-bottom: 8px; color: #30d158;">分拣进度</h4>
                            <div class="progress-bar" style="margin-bottom: 8px;">
                                <div class="progress-fill" style="width: 66%;"></div>
                            </div>
                            <div style="font-size: 12px; color: rgba(255,255,255,0.6);">2/3 商品已完成</div>
                        </div>
                    </div>

                    <!-- 中间分拣区域 -->
                    <div style="flex: 1;">
                        <div class="product-grid">
                            <div class="product-card completed">
                                <h3 style="margin-bottom: 12px;">蘑菇</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px; color: #30d158;">1箱</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 8px;">货架: A-3</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 100%; height: 100%; background: #30d158; border-radius: 2px;"></div>
                                </div>
                                <div style="margin-top: 8px; color: #30d158; font-size: 12px;">✓ 已完成</div>
                            </div>

                            <div class="product-card completed">
                                <h3 style="margin-bottom: 12px;">西红柿</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px; color: #30d158;">3斤</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 8px;">货架: B-1</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 100%; height: 100%; background: #30d158; border-radius: 2px;"></div>
                                </div>
                                <div style="margin-top: 8px; color: #30d158; font-size: 12px;">✓ 已完成</div>
                            </div>

                            <div class="product-card" style="border-color: #00d4ff; background: rgba(0,212,255,0.1);">
                                <h3 style="margin-bottom: 12px;">黄瓜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px; color: #00d4ff;">4斤</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 8px;">货架: B-2</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #00d4ff; border-radius: 2px;"></div>
                                </div>
                                <div style="margin-top: 8px; color: #00d4ff; font-size: 12px;">正在分拣...</div>
                            </div>

                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">胡萝卜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">2斤</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 8px;">货架: C-1</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #ff9500; border-radius: 2px;"></div>
                                </div>
                                <div style="margin-top: 8px; color: rgba(255,255,255,0.4); font-size: 12px;">等待中</div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧操作区域 -->
                    <div style="width: 150px;">
                        <div class="card" style="padding: 12px; margin-bottom: 12px;">
                            <h4 style="margin-bottom: 12px; color: #ff9500;">快捷操作</h4>
                            <div style="display: grid; gap: 8px;">
                                <button class="glow-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(45deg, #00d4ff, #0099cc);">
                                    扫码确认
                                </button>
                                <button class="glow-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(45deg, #ff9500, #ff6b00);">
                                    跳过商品
                                </button>
                                <button class="glow-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(45deg, #ff3b30, #ff1744);">
                                    缺货处理
                                </button>
                            </div>
                        </div>
                        <div class="card" style="padding: 12px;">
                            <h4 style="margin-bottom: 8px; color: #ff0080;">计时器</h4>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 700; color: #00d4ff; margin-bottom: 4px;">05:32</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">已用时间</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-top: 8px;">预计剩余: 2分钟</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 扫码页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">扫码识别</h2>

                    <!-- 扫码区域 -->
                    <div style="position: relative; margin: 40px 0; height: 300px; background: rgba(255,255,255,0.05); border-radius: 16px; overflow: hidden;">
                        <!-- 扫码框 -->
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; border: 2px solid #00d4ff; border-radius: 8px;">
                            <!-- 四个角的装饰 -->
                            <div style="position: absolute; top: -2px; left: -2px; width: 20px; height: 20px; border-top: 4px solid #00d4ff; border-left: 4px solid #00d4ff;"></div>
                            <div style="position: absolute; top: -2px; right: -2px; width: 20px; height: 20px; border-top: 4px solid #00d4ff; border-right: 4px solid #00d4ff;"></div>
                            <div style="position: absolute; bottom: -2px; left: -2px; width: 20px; height: 20px; border-bottom: 4px solid #00d4ff; border-left: 4px solid #00d4ff;"></div>
                            <div style="position: absolute; bottom: -2px; right: -2px; width: 20px; height: 20px; border-bottom: 4px solid #00d4ff; border-right: 4px solid #00d4ff;"></div>

                            <!-- 扫描线 -->
                            <div style="position: absolute; top: 0; left: 0; right: 0; height: 2px; background: linear-gradient(90deg, transparent, #00d4ff, transparent); animation: scan 2s infinite;"></div>
                        </div>

                        <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); color: rgba(255,255,255,0.8); font-size: 14px;">
                            请将条码对准扫描框
                        </div>
                    </div>

                    <!-- 扫码结果 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #30d158;">扫码结果</h3>
                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px; margin-bottom: 12px;">
                            <div style="color: #30d158; font-size: 20px;">✓</div>
                            <div>
                                <div style="font-weight: 600;">大白菜</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">条码: 1234567890123</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">库存: 50斤 | 货架: A-1</div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 8px;">
                            <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #30d158, #28a745);">
                                确认入库
                            </button>
                            <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #ff9500, #ff6b00);">
                                重新扫码
                            </button>
                        </div>
                    </div>

                    <!-- 历史记录 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px;">最近扫码</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; font-size: 14px;">小白菜</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">15:20</div>
                                </div>
                                <div style="color: #30d158; font-size: 12px;">已入库</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; font-size: 14px;">西红柿</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">15:18</div>
                                </div>
                                <div style="color: #30d158; font-size: 12px;">已入库</div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>扫码</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相机页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">智能识别</h2>

                    <!-- 相机预览区域 -->
                    <div style="position: relative; margin: 20px 0; height: 280px; background: linear-gradient(135deg, #1a1a2e, #16213e); border-radius: 16px; overflow: hidden; border: 1px solid rgba(255,255,255,0.1);">
                        <!-- 模拟相机界面 -->
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 150px; height: 150px; border: 2px dashed #00d4ff; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <svg style="width: 60px; height: 60px; fill: #00d4ff;" viewBox="0 0 24 24">
                                <use href="#icon-camera"></use>
                            </svg>
                        </div>

                        <!-- 识别框 -->
                        <div style="position: absolute; top: 30px; left: 30px; width: 80px; height: 60px; border: 2px solid #30d158; border-radius: 4px;">
                            <div style="position: absolute; top: -20px; left: 0; background: #30d158; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">大白菜</div>
                        </div>

                        <div style="position: absolute; top: 120px; right: 40px; width: 70px; height: 50px; border: 2px solid #ff9500; border-radius: 4px;">
                            <div style="position: absolute; top: -20px; left: 0; background: #ff9500; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">西红柿</div>
                        </div>

                        <!-- 拍照按钮 -->
                        <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%);">
                            <button style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(45deg, #00d4ff, #0099cc); border: 4px solid white; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                <div style="width: 20px; height: 20px; background: white; border-radius: 50%;"></div>
                            </button>
                        </div>

                        <div style="position: absolute; top: 20px; left: 50%; transform: translateX(-50%); color: rgba(255,255,255,0.8); font-size: 12px;">
                            AI智能识别中...
                        </div>
                    </div>

                    <!-- 识别结果 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #30d158;">识别结果</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">大白菜</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">置信度: 98.5%</div>
                                </div>
                                <div style="color: #30d158; font-size: 12px;">已识别</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff9500;">西红柿</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">置信度: 95.2%</div>
                                </div>
                                <div style="color: #ff9500; font-size: 12px;">已识别</div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 8px; margin-top: 16px;">
                            <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #30d158, #28a745);">
                                确认识别
                            </button>
                            <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #ff9500, #ff6b00);">
                                重新拍照
                            </button>
                        </div>
                    </div>

                    <!-- 识别历史 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px;">识别历史</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; font-size: 14px;">小白菜</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">15:22 | 置信度: 97.8%</div>
                                </div>
                                <div style="color: #30d158; font-size: 12px;">已确认</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; font-size: 14px;">黄瓜</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">15:20 | 置信度: 96.5%</div>
                                </div>
                                <div style="color: #30d158; font-size: 12px;">已确认</div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>扫码</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-camera"></use></svg>
                            <span>识别</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分拣大厅 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">分拣大厅</h2>

                    <!-- 实时状态 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">实时状态</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; text-align: center;">
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #30d158;">8</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">在线员工</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #ff9500;">23</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">待分拣</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #00d4ff;">5</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣中</div>
                            </div>
                        </div>
                    </div>

                    <!-- 订单列表 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px;">订单状态</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(0,212,255,0.1); border: 1px solid #00d4ff; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">张先生</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024001 | 电话: 138****1234</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">地址: 北京市朝阳区xxx街道 | 商品: 3件</div>
                                </div>
                                <div style="text-align: right;">
                                    <span class="status-dot status-processing"></span>
                                    <div style="font-size: 12px; color: #00d4ff;">李四分拣中</div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">李女士</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024002 | 电话: 139****5678</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">地址: 上海市浦东新区xxx路 | 商品: 5件</div>
                                </div>
                                <div style="text-align: right;">
                                    <span class="status-dot status-pending"></span>
                                    <div style="font-size: 12px; color: #ff9500;">等待分拣</div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">王先生</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024003 | 电话: 136****9012</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">地址: 广州市天河区xxx大道 | 商品: 2件</div>
                                </div>
                                <div style="text-align: right;">
                                    <span class="status-dot status-completed"></span>
                                    <div style="font-size: 12px; color: #30d158;">已完成</div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600;">赵女士</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024004 | 电话: 137****3456</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">地址: 深圳市南山区xxx街 | 商品: 4件</div>
                                </div>
                                <div style="text-align: right;">
                                    <span class="status-dot status-pending"></span>
                                    <div style="font-size: 12px; color: #ff9500;">等待分拣</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-sort"></use></svg>
                            <span>大厅</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理员页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">管理员控制台</h2>

                    <!-- 系统概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">系统概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #30d158;">¥128,560</div>
                                <div class="stat-label">本月销售额</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff9500;">¥85,420</div>
                                <div class="stat-label">本月成本</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #00d4ff;">¥43,140</div>
                                <div class="stat-label">本月利润</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #ff0080;">¥12,350</div>
                                <div class="stat-label">应收欠款</div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存状况 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">库存状况</h3>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                            <span>蔬菜类</span>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 60px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                    <div style="width: 75%; height: 100%; background: #30d158; border-radius: 3px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #30d158;">75%</span>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                            <span>肉类</span>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 60px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                    <div style="width: 45%; height: 100%; background: #ff9500; border-radius: 3px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #ff9500;">45%</span>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>海鲜类</span>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 60px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                    <div style="width: 20%; height: 100%; background: #ff3b30; border-radius: 3px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #ff3b30;">20%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 员工管理 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">员工管理</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">张三</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">在线 | 156单 | A+等级</div>
                                </div>
                                <div style="color: #30d158; font-weight: 600;">在线</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">李四</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">在线 | 142单 | A等级</div>
                                </div>
                                <div style="color: #30d158; font-weight: 600;">在线</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600;">王五</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">离线 | 128单 | A-等级</div>
                                </div>
                                <div style="color: rgba(255,255,255,0.4); font-weight: 600;">离线</div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>管理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 财务页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">财务管理</h2>

                    <!-- 财务概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">财务概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #30d158;">¥128,560</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月销售额</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #ff9500;">¥85,420</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月进货成本</div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #00d4ff;">¥43,140</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月利润</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #ff0080;">¥12,350</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">应收欠款</div>
                            </div>
                        </div>
                    </div>

                    <!-- 收支明细 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">今日收支</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">销售收入</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单156笔</div>
                                </div>
                                <div style="color: #30d158; font-weight: 600;">+¥4,280</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,59,48,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff3b30;">进货支出</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">采购3批次</div>
                                </div>
                                <div style="color: #ff3b30; font-weight: 600;">-¥2,850</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,59,48,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff3b30;">运营费用</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">人工+水电</div>
                                </div>
                                <div style="color: #ff3b30; font-weight: 600;">-¥680</div>
                            </div>
                        </div>
                        <div style="border-top: 1px solid rgba(255,255,255,0.1); margin-top: 12px; padding-top: 12px; display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600;">今日净利润</div>
                            <div style="color: #30d158; font-weight: 700; font-size: 18px;">+¥750</div>
                        </div>
                    </div>

                    <!-- 欠款明细 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">欠款明细</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">张记餐厅</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期15天</div>
                                </div>
                                <div style="color: #ff0080; font-weight: 600;">¥5,680</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">李家小炒</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期8天</div>
                                </div>
                                <div style="color: #ff0080; font-weight: 600;">¥3,420</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">王氏火锅</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期3天</div>
                                </div>
                                <div style="color: #ff0080; font-weight: 600;">¥3,250</div>
                            </div>
                        </div>

                        <button class="glow-btn" style="width: 100%; margin-top: 16px; background: linear-gradient(45deg, #ff0080, #ff4081);">
                            催收管理
                        </button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>主控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>财务</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        @keyframes scan {
            0% { top: 0; }
            100% { top: calc(100% - 2px); }
        }
    </style>
</body>
</html>
