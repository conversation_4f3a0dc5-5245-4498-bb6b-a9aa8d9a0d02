<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣APP - 大地黏土风格UI设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', 'YuanTi', sans-serif;
            background: linear-gradient(135deg, #F0E5D8 0%, #E6D5C7 100%);
            padding: 20px;
            min-height: 100vh;
            color: #5D4037;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 40px;
            color: #4E3B31;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-bottom: 40px;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #C39B7B;
            border-radius: 25px;
            background: #E6D5C7;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.15),
                inset 0 2px 4px rgba(255,255,255,0.3);
        }

        .screen {
            width: 100%;
            height: 100%;
            padding: 20px;
            position: relative;
        }

        /* 黏土风格组件 */
        .clay-button {
            background: #E6D5C7;
            border: none;
            border-radius: 20px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: 500;
            color: #5D4037;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.15),
                -6px -6px 12px rgba(255,255,255,0.7),
                inset 0 0 0 rgba(0,0,0,0);
        }

        .clay-button:hover {
            box-shadow: 
                3px 3px 6px rgba(0,0,0,0.15),
                -3px -3px 6px rgba(255,255,255,0.7),
                inset 3px 3px 6px rgba(0,0,0,0.1);
        }

        .clay-card {
            background: #E6D5C7;
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 
                8px 8px 16px rgba(0,0,0,0.15),
                -8px -8px 16px rgba(255,255,255,0.7);
        }

        .clay-input {
            background: #E6D5C7;
            border: none;
            border-radius: 15px;
            padding: 15px;
            width: 100%;
            font-size: 16px;
            color: #5D4037;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.15),
                inset -4px -4px 8px rgba(255,255,255,0.7);
        }

        .clay-input::placeholder {
            color: #B07D62;
        }

        /* 3D图标样式 */
        .icon-3d {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            background: linear-gradient(145deg, #C39B7B, #B07D62);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.2),
                -6px -6px 12px rgba(255,255,255,0.8);
        }

        /* 页面特定样式 */
        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h2 {
            font-size: 24px;
            margin-bottom: 10px;
            color: #4E3B31;
        }

        .grid-layout {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .feature-item {
            text-align: center;
            padding: 20px;
        }

        .feature-item h3 {
            font-size: 16px;
            margin-top: 10px;
            color: #5D4037;
        }

        /* 横屏布局样式 */
        .landscape-frame {
            width: 812px;
            height: 375px;
            border: 1px solid #C39B7B;
            border-radius: 25px;
            background: #E6D5C7;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.15),
                inset 0 2px 4px rgba(255,255,255,0.3);
        }

        .landscape-layout {
            display: flex;
            height: 100%;
            padding: 20px;
        }

        .sidebar {
            width: 200px;
            margin-right: 20px;
        }

        .product-grid {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 10px;
            margin-right: 20px;
        }

        .product-item {
            background: #E6D5C7;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #5D4037;
            box-shadow: 
                4px 4px 8px rgba(0,0,0,0.15),
                -4px -4px 8px rgba(255,255,255,0.7);
        }

        .sorted-list {
            width: 200px;
        }

        /* 数据可视化 */
        .chart-container {
            height: 200px;
            margin: 20px 0;
            position: relative;
        }

        .progress-bar {
            background: #E6D5C7;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.15),
                inset -4px -4px 8px rgba(255,255,255,0.7);
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #C39B7B, #B07D62);
            border-radius: 10px;
            transition: width 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 响应式调整 */
        @media (max-width: 1600px) {
            .screens-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .screens-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 800px) {
            .screens-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- SVG图标定义 -->
    <svg style="display: none;">
        <defs>
            <symbol id="warehouse" viewBox="0 0 24 24">
                <path fill="#FAF3E9" d="M12 2L2 7v10c0 5.55 3.84 9 9 9s9-3.45 9-9V7l-8-5z"/>
                <path fill="#C39B7B" d="M12 4L4 8v8c0 4.42 3.58 8 8 8s8-3.58 8-8V8l-8-4z"/>
            </symbol>
            <symbol id="order" viewBox="0 0 24 24">
                <rect fill="#FAF3E9" x="3" y="3" width="18" height="18" rx="3"/>
                <rect fill="#C39B7B" x="5" y="5" width="14" height="14" rx="2"/>
                <line stroke="#B07D62" x1="8" y1="10" x2="16" y2="10" stroke-width="2"/>
                <line stroke="#B07D62" x1="8" y1="14" x2="16" y2="14" stroke-width="2"/>
            </symbol>
            <symbol id="sort" viewBox="0 0 24 24">
                <circle fill="#FAF3E9" cx="12" cy="12" r="10"/>
                <circle fill="#C39B7B" cx="12" cy="12" r="8"/>
                <path fill="#B07D62" d="M8 10h8l-4 6z"/>
            </symbol>
            <symbol id="finance" viewBox="0 0 24 24">
                <circle fill="#FAF3E9" cx="12" cy="12" r="10"/>
                <circle fill="#C39B7B" cx="12" cy="12" r="8"/>
                <text x="12" y="16" text-anchor="middle" fill="#B07D62" font-size="8">¥</text>
            </symbol>
            <symbol id="sale" viewBox="0 0 24 24">
                <rect fill="#FAF3E9" x="2" y="6" width="20" height="12" rx="2"/>
                <rect fill="#C39B7B" x="4" y="8" width="16" height="8" rx="1"/>
                <circle fill="#B07D62" cx="12" cy="12" r="2"/>
            </symbol>
            <symbol id="dashboard" viewBox="0 0 24 24">
                <rect fill="#FAF3E9" x="2" y="2" width="20" height="20" rx="3"/>
                <rect fill="#C39B7B" x="4" y="4" width="16" height="16" rx="2"/>
                <rect fill="#B07D62" x="6" y="6" width="4" height="4" rx="1"/>
                <rect fill="#B07D62" x="14" y="6" width="4" height="4" rx="1"/>
                <rect fill="#B07D62" x="6" y="14" width="12" height="4" rx="1"/>
            </symbol>
            <symbol id="user" viewBox="0 0 24 24">
                <circle fill="#FAF3E9" cx="12" cy="12" r="10"/>
                <circle fill="#C39B7B" cx="12" cy="8" r="3"/>
                <path fill="#C39B7B" d="M6 18c0-4 2.7-6 6-6s6 2 6 6"/>
            </symbol>
            <symbol id="add-user" viewBox="0 0 24 24">
                <circle fill="#FAF3E9" cx="12" cy="12" r="10"/>
                <circle fill="#C39B7B" cx="10" cy="8" r="2"/>
                <path fill="#C39B7B" d="M6 16c0-3 1.8-4 4-4s4 1 4 4"/>
                <line stroke="#B07D62" x1="18" y1="8" x2="18" y2="14" stroke-width="2"/>
                <line stroke="#B07D62" x1="15" y1="11" x2="21" y2="11" stroke-width="2"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <h1 class="title">AI分拣APP - 大地黏土风格UI设计</h1>
        
        <!-- 第一行：核心功能页面 -->
        <div class="screens-grid">
            <!-- 入库管理 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d">
                            <svg width="30" height="30"><use href="#warehouse"></use></svg>
                        </div>
                        <h2>入库管理</h2>
                    </div>
                    
                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">今日入库统计</h3>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span>已入库:</span>
                            <span style="color: #C39B7B; font-weight: 600;">156件</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>待入库:</span>
                            <span style="color: #B07D62; font-weight: 600;">24件</span>
                        </div>
                    </div>

                    <input type="text" class="clay-input" placeholder="扫描或输入商品编码" style="margin: 20px 0;">
                    
                    <div class="clay-card">
                        <img src="https://source.unsplash.com/300x150/?warehouse,boxes" alt="商品图片" style="width: 100%; border-radius: 10px; margin-bottom: 10px;">
                        <h4>商品名称: 有机苹果</h4>
                        <p style="color: #B07D62; margin: 5px 0;">规格: 5kg/箱</p>
                        <p style="color: #B07D62;">数量: 20箱</p>
                    </div>

                    <button class="clay-button" style="width: 100%; margin-top: 20px;">确认入库</button>
                </div>
            </div>

            <!-- 录单功能 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d">
                            <svg width="30" height="30"><use href="#order"></use></svg>
                        </div>
                        <h2>录单功能</h2>
                    </div>
                    
                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">新建订单</h3>
                        <input type="text" class="clay-input" placeholder="客户姓名" style="margin-bottom: 15px;">
                        <input type="text" class="clay-input" placeholder="联系电话" style="margin-bottom: 15px;">
                        <input type="text" class="clay-input" placeholder="收货地址">
                    </div>

                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">商品清单</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span>有机苹果 x2</span>
                            <span style="color: #C39B7B;">¥68</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>新鲜橙子 x1</span>
                            <span style="color: #C39B7B;">¥25</span>
                        </div>
                        <hr style="margin: 15px 0; border: none; height: 1px; background: #D3C4B3;">
                        <div style="display: flex; justify-content: space-between; font-weight: 600;">
                            <span>总计:</span>
                            <span style="color: #B07D62;">¥93</span>
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button class="clay-button" style="flex: 1;">添加商品</button>
                        <button class="clay-button" style="flex: 1;">提交订单</button>
                    </div>
                </div>
            </div>

            <!-- 财务管理 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d">
                            <svg width="30" height="30"><use href="#finance"></use></svg>
                        </div>
                        <h2>财务管理</h2>
                    </div>
                    
                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">今日收益</h3>
                        <div style="text-align: center; margin: 20px 0;">
                            <div style="font-size: 32px; font-weight: 600; color: #C39B7B;">¥2,856</div>
                            <div style="color: #B07D62; margin-top: 5px;">较昨日 +12.5%</div>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                        <div style="text-align: center; color: #B07D62; font-size: 14px;">月度目标完成度: 75%</div>
                    </div>

                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">欠款管理</h3>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span>张三</span>
                            <span style="color: #B07D62;">¥156</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span>李四</span>
                            <span style="color: #B07D62;">¥89</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>王五</span>
                            <span style="color: #B07D62;">¥234</span>
                        </div>
                    </div>

                    <button class="clay-button" style="width: 100%; margin-top: 20px;">查看详细报表</button>
                </div>
            </div>

            <!-- 中控台 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d pulse">
                            <svg width="30" height="30"><use href="#dashboard"></use></svg>
                        </div>
                        <h2>中控台</h2>
                    </div>
                    
                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">实时数据</h3>
                        <div class="grid-layout">
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #C39B7B;">156</div>
                                <div style="color: #B07D62; font-size: 14px;">今日订单</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #C39B7B;">89</div>
                                <div style="color: #B07D62; font-size: 14px;">已分拣</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #C39B7B;">67</div>
                                <div style="color: #B07D62; font-size: 14px;">分拣中</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 24px; font-weight: 600; color: #C39B7B;">12</div>
                                <div style="color: #B07D62; font-size: 14px;">在线人数</div>
                            </div>
                        </div>
                    </div>

                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">分拣进度</h3>
                        <div style="margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>A区</span>
                                <span>85%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 85%;"></div>
                            </div>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>B区</span>
                                <span>62%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 62%;"></div>
                            </div>
                        </div>
                        <div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>C区</span>
                                <span>93%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 93%;"></div>
                            </div>
                        </div>
                    </div>

                    <button class="clay-button" style="width: 100%; margin-top: 20px;">刷新数据</button>
                </div>
            </div>
        </div>

        <!-- 第二行：会员中心和子账号管理 -->
        <div class="screens-grid">
            <!-- 会员中心 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d">
                            <svg width="30" height="30"><use href="#user"></use></svg>
                        </div>
                        <h2>会员中心</h2>
                    </div>
                    
                    <div class="clay-card">
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <img src="https://source.unsplash.com/80x80/?portrait,professional" alt="头像" style="width: 60px; height: 60px; border-radius: 50%; margin-right: 15px;">
                            <div>
                                <h3>张经理</h3>
                                <p style="color: #B07D62;">高级会员</p>
                            </div>
                        </div>
                    </div>

                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">功能模块</h3>
                        <div class="grid-layout">
                            <div class="feature-item">
                                <div class="icon-3d" style="width: 40px; height: 40px;">
                                    <svg width="20" height="20"><use href="#warehouse"></use></svg>
                                </div>
                                <h4 style="font-size: 14px;">入库管理</h4>
                            </div>
                            <div class="feature-item">
                                <div class="icon-3d" style="width: 40px; height: 40px;">
                                    <svg width="20" height="20"><use href="#order"></use></svg>
                                </div>
                                <h4 style="font-size: 14px;">录单功能</h4>
                            </div>
                            <div class="feature-item">
                                <div class="icon-3d" style="width: 40px; height: 40px;">
                                    <svg width="20" height="20"><use href="#sort"></use></svg>
                                </div>
                                <h4 style="font-size: 14px;">分拣系统</h4>
                            </div>
                            <div class="feature-item">
                                <div class="icon-3d" style="width: 40px; height: 40px;">
                                    <svg width="20" height="20"><use href="#finance"></use></svg>
                                </div>
                                <h4 style="font-size: 14px;">财务管理</h4>
                            </div>
                        </div>
                    </div>

                    <button class="clay-button" style="width: 100%; margin-top: 20px;">添加子账号</button>
                </div>
            </div>

            <!-- 添加子账号 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d">
                            <svg width="30" height="30"><use href="#add-user"></use></svg>
                        </div>
                        <h2>添加子账号</h2>
                    </div>
                    
                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">账号信息</h3>
                        <input type="text" class="clay-input" placeholder="用户名" style="margin-bottom: 15px;">
                        <input type="text" class="clay-input" placeholder="真实姓名" style="margin-bottom: 15px;">
                        <input type="password" class="clay-input" placeholder="密码">
                    </div>

                    <div class="clay-card">
                        <h3 style="margin-bottom: 15px;">权限分配</h3>
                        <div style="margin-bottom: 15px;">
                            <label style="display: flex; align-items: center; margin-bottom: 10px;">
                                <input type="checkbox" style="margin-right: 10px;" checked>
                                <span>入库管理</span>
                            </label>
                            <label style="display: flex; align-items: center; margin-bottom: 10px;">
                                <input type="checkbox" style="margin-right: 10px;">
                                <span>录单功能</span>
                            </label>
                            <label style="display: flex; align-items: center; margin-bottom: 10px;">
                                <input type="checkbox" style="margin-right: 10px;" checked>
                                <span>分拣系统</span>
                            </label>
                            <label style="display: flex; align-items: center;">
                                <input type="checkbox" style="margin-right: 10px;">
                                <span>财务管理</span>
                            </label>
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button class="clay-button" style="flex: 1;">取消</button>
                        <button class="clay-button" style="flex: 1;">创建账号</button>
                    </div>
                </div>
            </div>

            <!-- 子账号列表 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="header">
                        <div class="icon-3d">
                            <svg width="30" height="30"><use href="#user"></use></svg>
                        </div>
                        <h2>子账号列表</h2>
                    </div>
                    
                    <div class="clay-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div>
                                <h4>李分拣员</h4>
                                <p style="color: #B07D62; font-size: 14px;">分拣系统</p>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="clay-button" style="padding: 8px 15px; font-size: 14px;">编辑</button>
                                <button class="clay-button" style="padding: 8px 15px; font-size: 14px;">禁用</button>
                            </div>
                        </div>
                    </div>

                    <div class="clay-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div>
                                <h4>王录单员</h4>
                                <p style="color: #B07D62; font-size: 14px;">录单功能</p>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="clay-button" style="padding: 8px 15px; font-size: 14px;">编辑</button>
                                <button class="clay-button" style="padding: 8px 15px; font-size: 14px;">禁用</button>
                            </div>
                        </div>
                    </div>

                    <div class="clay-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div>
                                <h4>赵仓管员</h4>
                                <p style="color: #B07D62; font-size: 14px;">入库管理</p>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="clay-button" style="padding: 8px 15px; font-size: 14px;">编辑</button>
                                <button class="clay-button" style="padding: 8px 15px; font-size: 14px;">禁用</button>
                            </div>
                        </div>
                    </div>

                    <button class="clay-button" style="width: 100%; margin-top: 20px;">添加新账号</button>
                </div>
            </div>

            <!-- 占位 -->
            <div class="phone-frame">
                <div class="screen">
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #B07D62;">
                        <div style="text-align: center;">
                            <div class="icon-3d" style="margin: 0 auto 20px;">
                                <svg width="30" height="30"><use href="#dashboard"></use></svg>
                            </div>
                            <h3>更多功能</h3>
                            <p>敬请期待...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：横屏布局页面 -->
        <div style="display: flex; flex-direction: column; gap: 30px; margin-top: 40px;">
            <!-- 分拣系统横屏 -->
            <div style="text-align: center;">
                <h3 style="margin-bottom: 20px; color: #4E3B31;">分拣系统 - 横屏显示</h3>
                <div class="landscape-frame" style="margin: 0 auto;">
                    <div class="landscape-layout">
                        <div class="sidebar">
                            <h4 style="margin-bottom: 15px; color: #5D4037;">商品分类</h4>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">水果类</div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">蔬菜类</div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">肉类</div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">干货类</div>
                        </div>
                        
                        <div class="product-grid">
                            <div class="product-item">苹果</div>
                            <div class="product-item">橙子</div>
                            <div class="product-item">香蕉</div>
                            <div class="product-item">葡萄</div>
                            <div class="product-item">梨子</div>
                            <div class="product-item">桃子</div>
                            <div class="product-item">草莓</div>
                            <div class="product-item">蓝莓</div>
                            <div class="product-item">芒果</div>
                            <div class="product-item">菠萝</div>
                            <div class="product-item">柚子</div>
                            <div class="product-item">柠檬</div>
                        </div>
                        
                        <div class="sorted-list">
                            <h4 style="margin-bottom: 15px; color: #5D4037;">已分拣列表</h4>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px;">
                                <div style="font-size: 14px;">订单#001</div>
                                <div style="color: #B07D62; font-size: 12px;">苹果 x2, 橙子 x1</div>
                            </div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px;">
                                <div style="font-size: 14px;">订单#002</div>
                                <div style="color: #B07D62; font-size: 12px;">香蕉 x3, 葡萄 x1</div>
                            </div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px;">
                                <div style="font-size: 14px;">订单#003</div>
                                <div style="color: #B07D62; font-size: 12px;">梨子 x2, 桃子 x2</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速售卖横屏 -->
            <div style="text-align: center;">
                <h3 style="margin-bottom: 20px; color: #4E3B31;">快速售卖 - 横屏显示</h3>
                <div class="landscape-frame" style="margin: 0 auto;">
                    <div class="landscape-layout">
                        <div class="sidebar">
                            <h4 style="margin-bottom: 15px; color: #5D4037;">商品分类</h4>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">热销商品</div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">新鲜水果</div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">有机蔬菜</div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px; text-align: center;">特价商品</div>
                        </div>
                        
                        <div class="product-grid">
                            <div class="product-item">
                                <div style="font-size: 12px;">苹果</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥8/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">橙子</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥6/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">香蕉</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥5/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">葡萄</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥12/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">白菜</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥3/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">萝卜</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥2/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">土豆</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥4/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">西红柿</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥7/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">黄瓜</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥5/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">青椒</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥6/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">茄子</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥4/斤</div>
                            </div>
                            <div class="product-item">
                                <div style="font-size: 12px;">豆角</div>
                                <div style="color: #C39B7B; font-size: 10px;">¥8/斤</div>
                            </div>
                        </div>
                        
                        <div class="sorted-list">
                            <h4 style="margin-bottom: 15px; color: #5D4037;">购物车</h4>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px;">
                                <div style="display: flex; justify-content: space-between; font-size: 14px;">
                                    <span>苹果 x2斤</span>
                                    <span style="color: #C39B7B;">¥16</span>
                                </div>
                            </div>
                            <div class="clay-card" style="margin-bottom: 10px; padding: 10px;">
                                <div style="display: flex; justify-content: space-between; font-size: 14px;">
                                    <span>白菜 x1斤</span>
                                    <span style="color: #C39B7B;">¥3</span>
                                </div>
                            </div>
                            <div class="clay-card" style="margin-bottom: 15px; padding: 10px;">
                                <div style="display: flex; justify-content: space-between; font-weight: 600;">
                                    <span>总计:</span>
                                    <span style="color: #B07D62;">¥19</span>
                                </div>
                            </div>
                            <button class="clay-button" style="width: 100%;">结算</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 按钮点击效果
            const buttons = document.querySelectorAll('.clay-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 产品项点击效果
            const productItems = document.querySelectorAll('.product-item');
            productItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.background = '#D3C4B3';
                    setTimeout(() => {
                        this.style.background = '#E6D5C7';
                    }, 200);
                });
            });

            // 进度条动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
