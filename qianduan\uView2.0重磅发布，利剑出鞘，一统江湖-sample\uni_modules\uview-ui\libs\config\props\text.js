/*
 * <AUTHOR> LQ
 * @Description  :
 * @version      : 1.0
 * @Date         : 2021-08-20 16:44:21
 * @LastAuthor   : LQ
 * @lastTime     : 2021-08-20 17:23:58
 * @FilePath     : /u-view2.0/uview-ui/libs/config/props/text.js
 */
export default {
    // text 组件
    text: {
        type: '',
        show: true,
        text: '',
        prefixIcon: '',
        suffixIcon: '',
        mode: '',
        href: '',
        format: '',
        call: false,
        openType: '',
        bold: false,
        block: false,
        lines: '',
        color: '#303133',
        size: 15,
        iconStyle: () => ({
            fontSize: '15px'
        }),
        decoration: 'none',
        margin: 0,
        lineHeight: '',
        align: 'left',
        wordWrap: 'normal'
    }

}
