<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣APP - 大地黏土风格UI设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #F0E5D8 0%, #E6D5C7 100%);
            padding: 20px;
            min-height: 100vh;
            color: #5D4037;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 30px;
            color: #4E3B31;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .screens-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-bottom: 40px;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: #E6D5C7;
            border: 1px solid #D3C4B3;
            border-radius: 25px;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.15),
                inset 0 2px 4px rgba(255,255,255,0.3);
        }

        .screen {
            width: 100%;
            height: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .header {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            background: #E6D5C7;
            border-radius: 20px;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.1),
                inset -4px -4px 8px rgba(255,255,255,0.3);
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            color: #4E3B31;
        }

        .content {
            flex: 1;
            overflow-y: auto;
        }

        .clay-button {
            background: #E6D5C7;
            border: none;
            border-radius: 15px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: 500;
            color: #5D4037;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.15),
                -6px -6px 12px rgba(255,255,255,0.4);
            margin: 8px 0;
        }

        .clay-button:active {
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.15),
                inset -4px -4px 8px rgba(255,255,255,0.4);
        }

        .clay-card {
            background: #E6D5C7;
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 
                8px 8px 16px rgba(0,0,0,0.15),
                -8px -8px 16px rgba(255,255,255,0.4);
        }

        .clay-input {
            background: #E6D5C7;
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            color: #5D4037;
            width: 100%;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.15),
                inset -4px -4px 8px rgba(255,255,255,0.4);
            margin: 8px 0;
        }

        .clay-input::placeholder {
            color: #8D6E63;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #E6D5C7;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #D3C4B3;
            box-shadow: 
                inset 0 4px 8px rgba(255,255,255,0.3),
                0 -4px 8px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8D6E63;
            font-size: 12px;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            background: #C39B7B;
            border-radius: 50%;
            box-shadow: 
                3px 3px 6px rgba(0,0,0,0.15),
                -3px -3px 6px rgba(255,255,255,0.4);
        }

        .ai-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #C39B7B, #B07D62);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.2),
                -6px -6px 12px rgba(255,255,255,0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .list-item {
            background: #E6D5C7;
            border-radius: 12px;
            padding: 15px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 
                4px 4px 8px rgba(0,0,0,0.1),
                -4px -4px 8px rgba(255,255,255,0.3);
        }

        .list-item.completed {
            background: linear-gradient(135deg, #A5D6A7, #81C784);
            color: #2E7D32;
        }

        .grid-layout {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }

        .grid-item {
            aspect-ratio: 1;
            background: #E6D5C7;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            color: #5D4037;
            box-shadow: 
                4px 4px 8px rgba(0,0,0,0.15),
                -4px -4px 8px rgba(255,255,255,0.4);
        }

        .stats-card {
            background: #E6D5C7;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            box-shadow: 
                6px 6px 12px rgba(0,0,0,0.15),
                -6px -6px 12px rgba(255,255,255,0.4);
        }

        .stats-number {
            font-size: 24px;
            font-weight: 600;
            color: #C39B7B;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 12px;
            color: #8D6E63;
        }

        .chart-container {
            height: 150px;
            background: #E6D5C7;
            border-radius: 15px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.1),
                inset -4px -4px 8px rgba(255,255,255,0.3);
        }

        .landscape {
            transform: rotate(90deg);
            transform-origin: center;
            width: 812px;
            height: 375px;
            margin: 223px -223px;
        }

        .landscape .content {
            display: flex;
            flex-direction: row;
            gap: 20px;
        }

        .sidebar {
            width: 200px;
            background: #E6D5C7;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.1),
                inset -4px -4px 8px rgba(255,255,255,0.3);
        }

        .main-grid {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 15px;
        }

        .right-panel {
            width: 200px;
            background: #E6D5C7;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 
                inset 4px 4px 8px rgba(0,0,0,0.1),
                inset -4px -4px 8px rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">AI分拣APP - 大地黏土风格UI设计</h1>
        
        <!-- 第一行：核心功能页面 -->
        <div class="screens-grid">
            <!-- 入库管理列表 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>入库管理</h1>
                    </div>
                    <div class="content">
                        <div class="clay-button">新增入库单</div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">入库单 #001</div>
                                <div style="font-size: 12px; color: #8D6E63;">2024-01-15</div>
                            </div>
                            <div style="color: #FF9800; font-weight: 600;">未入库</div>
                        </div>
                        
                        <div class="list-item completed">
                            <div>
                                <div style="font-weight: 600;">入库单 #002</div>
                                <div style="font-size: 12px; color: #2E7D32;">2024-01-14</div>
                            </div>
                            <div style="font-weight: 600;">已入库</div>
                        </div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">入库单 #003</div>
                                <div style="font-size: 12px; color: #8D6E63;">2024-01-13</div>
                            </div>
                            <div style="color: #FF9800; font-weight: 600;">未入库</div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 入库详情 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>入库详情</h1>
                    </div>
                    <div class="content">
                        <div class="clay-card">
                            <div style="font-weight: 600; margin-bottom: 10px;">入库单 #001</div>
                            <div style="font-size: 14px; color: #8D6E63;">2024-01-15 09:30</div>
                        </div>
                        
                        <div class="list-item completed">
                            <div>
                                <div style="font-weight: 600;">小白菜</div>
                                <div style="font-size: 12px;">5斤</div>
                            </div>
                            <div style="color: #4CAF50;">✓</div>
                        </div>
                        
                        <div class="list-item completed">
                            <div>
                                <div style="font-weight: 600;">大白菜</div>
                                <div style="font-size: 12px;">6斤</div>
                            </div>
                            <div style="color: #4CAF50;">✓</div>
                        </div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">蘑菇</div>
                                <div style="font-size: 12px;">1箱</div>
                            </div>
                            <div style="color: #FF9800;">待入库</div>
                        </div>
                        
                        <div class="clay-button" style="margin-top: 20px;">确认入库</div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 录单功能 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>录单功能</h1>
                    </div>
                    <div class="content">
                        <input class="clay-input" placeholder="客户姓名" />
                        <input class="clay-input" placeholder="联系电话" />
                        
                        <div style="margin: 20px 0; font-weight: 600;">商品清单</div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">小白菜</div>
                                <div style="font-size: 12px; color: #8D6E63;">5斤 × ¥3.5</div>
                            </div>
                            <div style="font-weight: 600; color: #C39B7B;">¥17.5</div>
                        </div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">大白菜</div>
                                <div style="font-size: 12px; color: #8D6E63;">6斤 × ¥2.8</div>
                            </div>
                            <div style="font-weight: 600; color: #C39B7B;">¥16.8</div>
                        </div>
                        
                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">蘑菇</div>
                                <div style="font-size: 12px; color: #8D6E63;">1箱 × ¥25</div>
                            </div>
                            <div style="font-weight: 600; color: #C39B7B;">¥25</div>
                        </div>
                        
                        <div class="clay-card" style="margin-top: 20px;">
                            <div style="display: flex; justify-content: space-between; font-weight: 600; font-size: 18px;">
                                <span>总计：</span>
                                <span style="color: #C39B7B;">¥59.3</span>
                            </div>
                        </div>
                        
                        <div class="clay-button">确认录单</div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分拣系统（横屏） -->
            <div class="phone-frame">
                <div class="screen landscape">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>分拣系统</h1>
                    </div>
                    <div class="content">
                        <div class="sidebar">
                            <div style="font-weight: 600; margin-bottom: 15px;">分类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">叶菜类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">根茎类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">菌菇类</div>
                        </div>
                        <div class="main-grid">
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #81C784; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>小白菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #A5D6A7; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>大白菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C8E6C9; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>菠菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #DCEDC8; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>生菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #F1F8E9; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>韭菜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #FFCCBC; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>胡萝卜</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #FFE0B2; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>土豆</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #FFF3E0; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>洋葱</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #EFEBE9; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>蘑菇</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #F3E5F5; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>香菇</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #E8F5E8; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>金针菇</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #E3F2FD; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>平菇</span>
                            </div>
                        </div>
                        <div class="right-panel">
                            <div style="font-weight: 600; margin-bottom: 15px;">已分拣</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(76, 175, 80, 0.2); border-radius: 8px;">小白菜 5斤 ✓</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(76, 175, 80, 0.2); border-radius: 8px;">大白菜 6斤 ✓</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(255, 152, 0, 0.2); border-radius: 8px;">蘑菇 1箱 ⏳</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行：财务管理、快速售卖、中控台、会员中心 -->
        <div class="screens-grid">
            <!-- 财务管理 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>财务管理</h1>
                    </div>
                    <div class="content">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="stats-card">
                                <div class="stats-number">¥12,580</div>
                                <div class="stats-label">今日收益</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">¥3,240</div>
                                <div class="stats-label">待收款</div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <svg width="300" height="120" viewBox="0 0 300 120">
                                <defs>
                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#C39B7B;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#C39B7B;stop-opacity:0.2" />
                                    </linearGradient>
                                </defs>
                                <path d="M20,100 L50,80 L80,60 L110,70 L140,40 L170,50 L200,30 L230,45 L260,25 L280,35"
                                      stroke="#C39B7B" stroke-width="3" fill="none"/>
                                <path d="M20,100 L50,80 L80,60 L110,70 L140,40 L170,50 L200,30 L230,45 L260,25 L280,35 L280,100 L20,100"
                                      fill="url(#gradient)"/>
                            </svg>
                        </div>

                        <div class="clay-card">
                            <div style="font-weight: 600; margin-bottom: 15px;">收支明细</div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                                <span>蔬菜销售</span>
                                <span style="color: #4CAF50; font-weight: 600;">+¥2,580</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                                <span>进货成本</span>
                                <span style="color: #F44336; font-weight: 600;">-¥1,200</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0;">
                                <span>运输费用</span>
                                <span style="color: #F44336; font-weight: 600;">-¥150</span>
                            </div>
                        </div>

                        <div class="clay-button">查看详细报表</div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速售卖（横屏） -->
            <div class="phone-frame">
                <div class="screen landscape">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>快速售卖</h1>
                    </div>
                    <div class="content">
                        <div class="sidebar">
                            <div style="font-weight: 600; margin-bottom: 15px;">分类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">叶菜类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">根茎类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">菌菇类</div>
                            <div class="clay-button" style="margin: 5px 0; padding: 10px; font-size: 14px;">特价商品</div>
                        </div>
                        <div class="main-grid">
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #81C784; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>小白菜</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥3.5/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #A5D6A7; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>大白菜</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥2.8/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #C8E6C9; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>菠菜</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥4.2/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #DCEDC8; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>生菜</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥3.8/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #F1F8E9; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>韭菜</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥5.0/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #FFCCBC; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>胡萝卜</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥3.2/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #FFE0B2; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>土豆</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥2.5/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #FFF3E0; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>洋葱</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥3.0/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #EFEBE9; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>蘑菇</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥25/箱</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #F3E5F5; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>香菇</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥8.5/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #E8F5E8; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>金针菇</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥6.0/斤</span>
                            </div>
                            <div class="grid-item" style="cursor: pointer;">
                                <div style="width: 30px; height: 30px; background: #E3F2FD; border-radius: 50%; margin-bottom: 5px;"></div>
                                <span>平菇</span>
                                <span style="color: #C39B7B; font-weight: 600;">¥7.2/斤</span>
                            </div>
                        </div>
                        <div class="right-panel">
                            <div style="font-weight: 600; margin-bottom: 15px;">购物车</div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(195, 155, 123, 0.2); border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span>小白菜 2斤</span>
                                    <span>¥7.0</span>
                                </div>
                            </div>
                            <div style="font-size: 12px; margin: 5px 0; padding: 8px; background: rgba(195, 155, 123, 0.2); border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span>蘑菇 1箱</span>
                                    <span>¥25.0</span>
                                </div>
                            </div>
                            <div style="margin-top: 20px; padding: 10px; background: #C39B7B; color: white; border-radius: 10px; text-align: center; font-weight: 600;">
                                总计: ¥32.0
                            </div>
                            <div class="clay-button" style="margin-top: 10px; font-size: 14px;">结算</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中控台 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>中控台</h1>
                    </div>
                    <div class="content">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="stats-card">
                                <div class="stats-number">156</div>
                                <div class="stats-label">今日订单</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">89</div>
                                <div class="stats-label">分拣完成</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="stats-card">
                                <div class="stats-number">23</div>
                                <div class="stats-label">分拣中</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">12</div>
                                <div class="stats-label">在线人数</div>
                            </div>
                        </div>

                        <div class="clay-card">
                            <div style="font-weight: 600; margin-bottom: 15px;">实时状态</div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                                <span>分拣员A</span>
                                <span style="color: #4CAF50; font-weight: 600;">工作中</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                                <span>分拣员B</span>
                                <span style="color: #4CAF50; font-weight: 600;">工作中</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(255, 152, 0, 0.1); border-radius: 8px;">
                                <span>分拣员C</span>
                                <span style="color: #FF9800; font-weight: 600;">休息中</span>
                            </div>
                        </div>

                        <div class="chart-container">
                            <div style="text-align: center; color: #8D6E63;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">效率统计</div>
                                <div style="width: 80px; height: 80px; border: 8px solid #C39B7B; border-top: 8px solid #E6D5C7; border-radius: 50%; margin: 0 auto; animation: spin 2s linear infinite;"></div>
                            </div>
                        </div>

                        <style>
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        </style>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会员中心 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>会员中心</h1>
                    </div>
                    <div class="content">
                        <div class="clay-card">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 50%; margin-right: 15px;"></div>
                                <div>
                                    <div style="font-weight: 600; font-size: 18px;">张三</div>
                                    <div style="color: #8D6E63; font-size: 14px;">高级会员</div>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 20px 0; font-weight: 600;">功能模块</div>

                        <div class="grid-layout" style="grid-template-columns: repeat(3, 1fr);">
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C39B7B; border-radius: 50%; margin-bottom: 8px;"></div>
                                <span>入库管理</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C39B7B; border-radius: 50%; margin-bottom: 8px;"></div>
                                <span>录单功能</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C39B7B; border-radius: 50%; margin-bottom: 8px;"></div>
                                <span>分拣系统</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C39B7B; border-radius: 50%; margin-bottom: 8px;"></div>
                                <span>财务管理</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C39B7B; border-radius: 50%; margin-bottom: 8px;"></div>
                                <span>快速售卖</span>
                            </div>
                            <div class="grid-item">
                                <div style="width: 30px; height: 30px; background: #C39B7B; border-radius: 50%; margin-bottom: 8px;"></div>
                                <span>中控台</span>
                            </div>
                        </div>

                        <div class="clay-button">添加子账号</div>
                        <div class="clay-button">子账号管理</div>
                        <div class="clay-button">权限设置</div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：添加子账号页面 -->
        <div class="screens-grid">
            <!-- 添加子账号 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>添加子账号</h1>
                    </div>
                    <div class="content">
                        <input class="clay-input" placeholder="用户名" />
                        <input class="clay-input" placeholder="密码" />
                        <input class="clay-input" placeholder="确认密码" />
                        <input class="clay-input" placeholder="姓名" />
                        <input class="clay-input" placeholder="手机号" />

                        <div style="margin: 20px 0; font-weight: 600;">权限分配</div>

                        <div class="clay-card">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>入库管理</span>
                                <div style="width: 40px; height: 20px; background: #4CAF50; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>录单功能</span>
                                <div style="width: 40px; height: 20px; background: #4CAF50; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>分拣系统</span>
                                <div style="width: 40px; height: 20px; background: #BDBDBD; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>财务管理</span>
                                <div style="width: 40px; height: 20px; background: #BDBDBD; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0;">
                                <span>快速售卖</span>
                                <div style="width: 40px; height: 20px; background: #4CAF50; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                        </div>

                        <div class="clay-button">创建账号</div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 子账号列表 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>子账号管理</h1>
                    </div>
                    <div class="content">
                        <div class="clay-button">添加新账号</div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">李四</div>
                                <div style="font-size: 12px; color: #8D6E63;">分拣员 | 13800138001</div>
                                <div style="font-size: 12px; color: #4CAF50;">在线</div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <div style="padding: 5px 10px; background: #C39B7B; color: white; border-radius: 8px; font-size: 12px;">编辑</div>
                                <div style="padding: 5px 10px; background: #F44336; color: white; border-radius: 8px; font-size: 12px;">删除</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">王五</div>
                                <div style="font-size: 12px; color: #8D6E63;">录单员 | 13900139001</div>
                                <div style="font-size: 12px; color: #FF9800;">离线</div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <div style="padding: 5px 10px; background: #C39B7B; color: white; border-radius: 8px; font-size: 12px;">编辑</div>
                                <div style="padding: 5px 10px; background: #F44336; color: white; border-radius: 8px; font-size: 12px;">删除</div>
                            </div>
                        </div>

                        <div class="list-item">
                            <div>
                                <div style="font-weight: 600;">赵六</div>
                                <div style="font-size: 12px; color: #8D6E63;">售卖员 | 13700137001</div>
                                <div style="font-size: 12px; color: #4CAF50;">在线</div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <div style="padding: 5px 10px; background: #C39B7B; color: white; border-radius: 8px; font-size: 12px;">编辑</div>
                                <div style="padding: 5px 10px; background: #F44336; color: white; border-radius: 8px; font-size: 12px;">删除</div>
                            </div>
                        </div>

                        <div class="clay-card" style="margin-top: 20px;">
                            <div style="font-weight: 600; margin-bottom: 10px;">权限说明</div>
                            <div style="font-size: 14px; color: #8D6E63; line-height: 1.5;">
                                • 分拣员：仅可使用分拣系统<br>
                                • 录单员：可使用录单功能和入库管理<br>
                                • 售卖员：可使用快速售卖功能<br>
                                • 管理员：拥有所有权限
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限编辑页面 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>权限编辑</h1>
                    </div>
                    <div class="content">
                        <div class="clay-card">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 50%; margin-right: 15px;"></div>
                                <div>
                                    <div style="font-weight: 600; font-size: 16px;">李四</div>
                                    <div style="color: #8D6E63; font-size: 14px;">13800138001</div>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 20px 0; font-weight: 600;">功能权限</div>

                        <div class="clay-card">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">入库管理</div>
                                    <div style="font-size: 12px; color: #8D6E63;">管理进货和入库操作</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #4CAF50; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">录单功能</div>
                                    <div style="font-size: 12px; color: #8D6E63;">为客户录入订单信息</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #BDBDBD; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">分拣系统</div>
                                    <div style="font-size: 12px; color: #8D6E63;">进行商品分拣操作</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #4CAF50; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; right: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">财务管理</div>
                                    <div style="font-size: 12px; color: #8D6E63;">查看财务数据和报表</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #BDBDBD; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 15px 0;">
                                <div>
                                    <div style="font-weight: 600;">快速售卖</div>
                                    <div style="font-size: 12px; color: #8D6E63;">现场销售商品</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #BDBDBD; border-radius: 10px; position: relative;">
                                    <div style="width: 18px; height: 18px; background: white; border-radius: 50%; position: absolute; top: 1px; left: 1px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                                </div>
                            </div>
                        </div>

                        <div class="clay-button">保存权限</div>
                        <div class="clay-button" style="background: #F44336; color: white;">重置密码</div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI语音助手页面 -->
            <div class="phone-frame">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>100%</span>
                    </div>
                    <div class="header">
                        <h1>AI语音助手</h1>
                    </div>
                    <div class="content">
                        <div style="text-align: center; margin: 40px 0;">
                            <div style="width: 150px; height: 150px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; box-shadow: 0 20px 40px rgba(0,0,0,0.2); animation: pulse 2s infinite;">
                                <div style="color: white; font-size: 48px; font-weight: 600;">AI</div>
                            </div>
                            <div style="margin-top: 20px; font-size: 18px; font-weight: 600; color: #5D4037;">点击开始对话</div>
                        </div>

                        <div class="clay-card">
                            <div style="font-weight: 600; margin-bottom: 15px;">语音指令示例</div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(195, 155, 123, 0.1); border-radius: 8px; font-size: 14px;">
                                "帮我查看今天的订单数量"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(195, 155, 123, 0.1); border-radius: 8px; font-size: 14px;">
                                "录入一个新订单"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(195, 155, 123, 0.1); border-radius: 8px; font-size: 14px;">
                                "查看分拣进度"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: rgba(195, 155, 123, 0.1); border-radius: 8px; font-size: 14px;">
                                "今日财务报表"
                            </div>
                        </div>

                        <div class="clay-card">
                            <div style="font-weight: 600; margin-bottom: 10px;">对话记录</div>
                            <div style="font-size: 14px; color: #8D6E63; line-height: 1.5;">
                                <div style="margin: 8px 0;">
                                    <span style="color: #C39B7B; font-weight: 600;">用户：</span>今天有多少订单？
                                </div>
                                <div style="margin: 8px 0;">
                                    <span style="color: #4CAF50; font-weight: 600;">AI：</span>今天共有156个订单，其中89个已完成分拣。
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px; margin-top: 20px;">
                            <div class="clay-button" style="flex: 1;">清除记录</div>
                            <div class="clay-button" style="flex: 1;">设置</div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>功能</span>
                        </div>
                        <div class="nav-item">
                            <div class="ai-button">AI</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon"></div>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
