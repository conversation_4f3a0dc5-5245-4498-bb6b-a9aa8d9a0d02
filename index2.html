<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣系统 - 弥散色设计</title>
    <style>
        :root {
            --primary-100: #d4eaf7;
            --primary-200: #b6ccd8;
            --primary-300: #3b3c3d;
            --accent-100: #71c4ef;
            --accent-200: #00668c;
            --text-100: #1d1c1c;
            --text-200: #313d44;
            --bg-100: #fffefb;
            --bg-200: #f5f4f1;
            --bg-300: #cccbc8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background:
                radial-gradient(circle at 15% 85%, rgba(113, 196, 239, 0.4) 0%, transparent 60%),
                radial-gradient(circle at 85% 15%, rgba(168, 85, 247, 0.35) 0%, transparent 65%),
                radial-gradient(circle at 50% 50%, rgba(74, 222, 128, 0.25) 0%, transparent 70%),
                radial-gradient(circle at 25% 25%, rgba(251, 191, 36, 0.3) 0%, transparent 55%),
                radial-gradient(circle at 75% 75%, rgba(239, 68, 68, 0.2) 0%, transparent 60%),
                linear-gradient(135deg, #fefefe 0%, #f8fafc 30%, #f1f5f9 70%, #e2e8f0 100%);
            background-attachment: fixed;
            color: var(--text-100);
            overflow-x: auto;
            padding: 20px;
            position: relative;
            min-height: 100vh;
        }

        /* 多层噪点和迷雾效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0),
                radial-gradient(circle at 15px 15px, rgba(113, 196, 239, 0.1) 1px, transparent 0),
                radial-gradient(circle at 30px 30px, rgba(168, 85, 247, 0.08) 1px, transparent 0);
            background-size: 25px 25px, 45px 45px, 60px 60px;
            opacity: 0.6;
            pointer-events: none;
            z-index: 1;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.08) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 25%, rgba(113, 196, 239, 0.05) 50%, transparent 75%),
                linear-gradient(135deg, transparent 35%, rgba(168, 85, 247, 0.04) 50%, transparent 65%);
            backdrop-filter: blur(0.5px);
            pointer-events: none;
            z-index: 2;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            max-width: 2400px;
            margin: 0 auto;
            justify-content: center;
            position: relative;
            z-index: 2;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid rgba(59, 60, 61, 0.2);
            border-radius: 40px;
            background: rgba(255, 254, 251, 0.8);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .landscape-frame {
            width: 812px;
            height: 375px;
            border: 1px solid rgba(59, 60, 61, 0.2);
            border-radius: 40px;
            background: rgba(255, 254, 251, 0.8);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .screen {
            width: 100%;
            height: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .page {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 玻璃拟态卡片 */
        .glass-card {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 4px 16px rgba(113, 196, 239, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6),
                inset 0 -1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            opacity: 0.6;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.35);
            transform: translateY(-1px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.12),
                0 6px 20px rgba(113, 196, 239, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.7),
                inset 0 -1px 0 rgba(255, 255, 255, 0.3);
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-100);
            margin-bottom: 24px;
            text-align: center;
            background: linear-gradient(135deg, var(--accent-200), var(--accent-100));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .input-field {
            width: 100%;
            padding: 16px 20px;
            background: rgba(255, 255, 255, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            color: var(--text-100);
            font-size: 16px;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--accent-100);
            background: rgba(255, 255, 255, 0.6);
            box-shadow: 0 0 0 3px rgba(113, 196, 239, 0.2);
        }

        .input-field::placeholder {
            color: var(--text-200);
            opacity: 0.7;
        }

        .glow-btn {
            background: linear-gradient(135deg, var(--accent-100), var(--accent-200));
            border: none;
            border-radius: 16px;
            padding: 16px 24px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 
                0 8px 24px rgba(113, 196, 239, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .glow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 12px 32px rgba(113, 196, 239, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* 标题层级样式 */
        .page-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-100);
            margin: 0;
            letter-spacing: -0.02em;
        }

        .section-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-100);
            margin: 0 0 12px 0;
            letter-spacing: -0.01em;
        }

        .card-title {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-100);
            margin: 0 0 8px 0;
        }

        .label-text {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-200);
            margin: 0 0 6px 0;
        }

        .nav-bar {
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(25px);
            border-radius: 18px;
            padding: 8px 6px;
            margin-top: auto;
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            position: relative;
            z-index: 10;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 8px;
            border-radius: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            min-width: 50px;
        }

        .nav-item:hover {
            background: rgba(113, 196, 239, 0.1);
            transform: translateY(-1px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, rgba(113, 196, 239, 0.25), rgba(168, 85, 247, 0.15));
            color: var(--accent-200);
            box-shadow:
                0 4px 12px rgba(113, 196, 239, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .nav-item .icon {
            width: 20px;
            height: 20px;
            margin-bottom: 2px;
            fill: currentColor;
            transition: all 0.3s ease;
        }

        .nav-item.active .icon {
            transform: scale(1.1);
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
        }

        .icon {
            fill: currentColor;
        }

        /* 动态折线图 */
        .chart-container {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
        }

        .chart-line {
            stroke: var(--accent-100);
            stroke-width: 3;
            fill: none;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 2s ease-in-out forwards;
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        @keyframes conveyor {
            0% {
                background-position: 0% 50%;
            }
            100% {
                background-position: 100% 50%;
            }
        }

        /* 状态指示器 */
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online { background: #4ade80; }
        .status-processing { background: var(--accent-100); }
        .status-pending { background: #fbbf24; }
        .status-offline { background: var(--bg-300); }

        /* 粒子动画 */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--accent-100);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
            opacity: 0.6;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- SVG图标定义 -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-login" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17C12.8 17 13.5 16.3 13.5 15.5S12.8 14 12 14 10.5 14.7 10.5 15.5 11.2 17 12 17Z"/>
            </symbol>
            <symbol id="icon-dashboard" viewBox="0 0 24 24">
                <path d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/>
            </symbol>
            <symbol id="icon-scan" viewBox="0 0 24 24">
                <path d="M9,2V5H7V4A1,1 0 0,0 6,3H3A1,1 0 0,0 2,4V7A1,1 0 0,0 3,8H4V6H7V9H2V15H7V12H9V15A1,1 0 0,0 10,16H13A1,1 0 0,0 14,15V12H17V15H20V9H15V12H13V9A1,1 0 0,0 12,8H9A1,1 0 0,0 8,9V12H6V6H9V2M15,2V5H17V4A1,1 0 0,1 18,3H21A1,1 0 0,1 22,4V7A1,1 0 0,1 21,8H20V6H17V9H22V15H17V12H15V15A1,1 0 0,1 14,16H11A1,1 0 0,1 10,15V12H8V15H5V9H10V12H12V9A1,1 0 0,1 13,8H16A1,1 0 0,1 17,9V12H19V6H16V2H15Z"/>
            </symbol>
            <symbol id="icon-order" viewBox="0 0 24 24">
                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
            </symbol>
            <symbol id="icon-profile" viewBox="0 0 24 24">
                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
            </symbol>
            <symbol id="icon-analytics" viewBox="0 0 24 24">
                <path d="M22,21H2V3H4V19H6V17H10V19H12V16H16V19H18V17H22V21Z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
            </symbol>
            <symbol id="icon-member" viewBox="0 0 24 24">
                <path d="M16,4C18.2,4 20,5.8 20,8C20,10.2 18.2,12 16,12C13.8,12 12,10.2 12,8C12,5.8 13.8,4 16,4M16,14C20.4,14 24,15.8 24,18V20H8V18C8,15.8 11.6,14 16,14M8.5,4C10.4,4 12,5.6 12,7.5C12,9.4 10.4,11 8.5,11C6.6,11 5,9.4 5,7.5C5,5.6 6.6,4 8.5,4M8.5,13C11.5,13 14,14.2 14,15.8V17H3V15.8C3,14.2 5.5,13 8.5,13Z"/>
            </symbol>
            <symbol id="icon-finance" viewBox="0 0 24 24">
                <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
            </symbol>
            <symbol id="icon-sort" viewBox="0 0 24 24">
                <path d="M9,3L5,7H8V14H10V7H13M16,17V10H14V17H11L15,21L19,17H16Z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <!-- 第一行：登录页、入库模块、订单录入、分拣任务 -->

        <!-- 登录页 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div class="particles">
                        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                        <div class="particle" style="left: 30%; animation-delay: 1s;"></div>
                        <div class="particle" style="left: 50%; animation-delay: 2s;"></div>
                        <div class="particle" style="left: 70%; animation-delay: 3s;"></div>
                        <div class="particle" style="left: 90%; animation-delay: 4s;"></div>
                    </div>

                    <div style="text-align: center; margin-top: 120px;">
                        <div style="width: 100px; height: 100px; margin: 0 auto 30px; background: linear-gradient(135deg, var(--accent-100), var(--accent-200)); border-radius: 30px; display: flex; align-items: center; justify-content: center; box-shadow: 0 20px 40px rgba(113, 196, 239, 0.3);">
                            <svg class="icon" style="width: 50px; height: 50px; fill: white;">
                                <use href="#icon-login"></use>
                            </svg>
                        </div>
                        <h1 style="font-size: 28px; font-weight: 800; margin-bottom: 8px; background: linear-gradient(135deg, var(--accent-200), #a855f7); -webkit-background-clip: text; -webkit-text-fill-color: transparent; letter-spacing: -0.02em;">AI智能分拣系统</h1>
                        <p style="color: var(--text-200); margin-bottom: 60px; font-size: 14px; font-weight: 500; opacity: 0.8;">科技赋能，智慧分拣</p>
                    </div>

                    <div class="glass-card" style="margin: 0 20px;">
                        <input type="text" class="input-field" placeholder="请输入会员账号">
                        <input type="password" class="input-field" placeholder="请输入密码">

                        <button class="glow-btn" style="width: 100%; margin-top: 8px;">
                            登录系统
                        </button>

                        <div style="text-align: center; margin-top: 20px;">
                            <a href="#" style="color: var(--accent-200); text-decoration: none; font-size: 14px;">忘记密码？</a>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 40px;">
                        <p style="color: var(--text-200); font-size: 12px; opacity: 0.7;">
                            权限验证 • 模块检测 • 安全登录
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 入库模块 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <button style="background: none; border: none; color: var(--accent-200); font-size: 18px; margin-right: 12px;">←</button>
                        <h2 class="page-title">商品入库</h2>
                    </div>

                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="glow-btn" style="font-size: 12px; padding: 6px 14px;">未入库</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 6px 14px; color: var(--text-100); font-size: 12px;">已入库</button>
                    </div>

                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div>
                                <div class="card-title">采购单 #PO2024001</div>
                                <div class="label-text">供应商: 新鲜蔬菜批发</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div class="label-text" style="margin-bottom: 12px;">
                            商品: 8种 | 预计到货: 今日14:00
                        </div>
                        <button class="glow-btn" style="font-size: 12px; padding: 6px 14px;">开始入库</button>
                    </div>

                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div>
                                <div class="card-title">采购单 #PO2024002</div>
                                <div class="label-text">供应商: 优质肉类配送</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div class="label-text" style="margin-bottom: 12px;">
                            商品: 5种 | 预计到货: 今日16:30
                        </div>
                        <button class="glow-btn" style="font-size: 12px; padding: 6px 14px;">开始入库</button>
                    </div>

                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div>
                                <div class="card-title">采购单 #PO2024003</div>
                                <div class="label-text">供应商: 海鲜直供</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div class="label-text" style="margin-bottom: 12px;">
                            商品: 3种 | 预计到货: 明日09:00
                        </div>
                        <button class="glow-btn" style="font-size: 12px; padding: 6px 14px;">开始入库</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单录入 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <button style="background: none; border: none; color: var(--accent-200); font-size: 18px; margin-right: 12px;">←</button>
                        <h2 class="page-title">订单录入</h2>
                    </div>

                    <div class="glass-card">
                        <label class="label-text">客户姓名</label>
                        <input type="text" class="input-field" placeholder="请输入客户姓名" value="张先生">
                    </div>

                    <div class="glass-card">
                        <label class="label-text">联系电话</label>
                        <input type="text" class="input-field" placeholder="请输入联系电话" value="138****5678">
                    </div>

                    <div class="glass-card">
                        <label class="label-text">配送地址</label>
                        <input type="text" class="input-field" placeholder="请输入配送地址" value="北京市朝阳区xxx小区">
                    </div>

                    <!-- 商品选择 -->
                    <div class="glass-card">
                        <h4 class="section-title">商品清单</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                            <div style="background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 8px; padding: 8px; text-align: center;">
                                <div class="card-title">蘑菇</div>
                                <div class="label-text">1箱</div>
                            </div>
                            <div style="background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 8px; padding: 8px; text-align: center;">
                                <div class="card-title">西红柿</div>
                                <div class="label-text">3斤</div>
                            </div>
                            <div style="background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 8px; padding: 8px; text-align: center;">
                                <div class="card-title">黄瓜</div>
                                <div class="label-text">4斤</div>
                            </div>
                            <div style="background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 8px; padding: 8px; text-align: center;">
                                <div class="card-title">胡萝卜</div>
                                <div class="label-text">2斤</div>
                            </div>
                        </div>
                        <button style="width: 100%; padding: 8px; background: transparent; border: 1px dashed var(--accent-100); border-radius: 8px; color: var(--accent-200); font-size: 14px;">+ 添加商品</button>
                    </div>

                    <!-- 配送时间 -->
                    <div class="glass-card">
                        <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">配送时间</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="glow-btn" style="padding: 8px; font-size: 12px;">今日送达</button>
                            <button style="padding: 8px; background: transparent; border: 1px solid var(--bg-300); border-radius: 8px; color: var(--text-200); font-size: 12px;">明日送达</button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-top: 20px;">
                        <button style="padding: 16px; background: transparent; border: 1px solid var(--bg-300); border-radius: 16px; color: var(--text-200); font-size: 16px; font-weight: 600;">保存草稿</button>
                        <button class="glow-btn" style="padding: 16px; font-size: 16px; font-weight: 600;">提交订单</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分拣任务 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <button style="background: none; border: none; color: var(--accent-200); font-size: 18px; margin-right: 12px;">←</button>
                        <h2 class="page-title">分拣任务</h2>
                    </div>

                    <!-- 任务统计 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; margin-bottom: 20px;">
                        <div class="glass-card" style="padding: 10px; text-align: center;">
                            <div style="font-size: 18px; font-weight: 700; color: var(--accent-200);">12</div>
                            <div class="label-text">待分拣</div>
                        </div>
                        <div class="glass-card" style="padding: 10px; text-align: center;">
                            <div style="font-size: 18px; font-weight: 700; color: #fbbf24;">5</div>
                            <div class="label-text">进行中</div>
                        </div>
                        <div class="glass-card" style="padding: 10px; text-align: center;">
                            <div style="font-size: 18px; font-weight: 700; color: #4ade80;">28</div>
                            <div class="label-text">已完成</div>
                        </div>
                    </div>

                    <!-- 任务列表 -->
                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-100);">订单 #ORD2024001</div>
                                <div style="font-size: 12px; color: var(--text-200);">客户: 张先生 | 商品: 4种</div>
                            </div>
                            <span style="background: rgba(251, 191, 36, 0.2); color: #f59e0b; padding: 4px 8px; border-radius: 8px; font-size: 12px; font-weight: 600;">进行中</span>
                        </div>
                        <div style="background: var(--bg-200); border-radius: 8px; height: 4px; margin-bottom: 8px;">
                            <div style="background: linear-gradient(90deg, #f59e0b, #fbbf24); height: 100%; width: 60%; border-radius: 8px;"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: var(--text-200); font-size: 12px;">预计完成: 15:30</span>
                            <button class="glow-btn" style="padding: 6px 12px; font-size: 12px;">继续分拣</button>
                        </div>
                    </div>

                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-100);">订单 #ORD2024002</div>
                                <div style="font-size: 12px; color: var(--text-200);">客户: 李女士 | 商品: 6种</div>
                            </div>
                            <span style="background: rgba(113, 196, 239, 0.2); color: var(--accent-200); padding: 4px 8px; border-radius: 8px; font-size: 12px; font-weight: 600;">待分拣</span>
                        </div>
                        <div style="font-size: 12px; color: var(--text-200); margin-bottom: 12px;">
                            优先级: 高 | 配送时间: 今日18:00
                        </div>
                        <button class="glow-btn" style="width: 100%; padding: 8px; font-size: 14px;">开始分拣</button>
                    </div>

                    <div class="glass-card" style="opacity: 0.7;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-100);">订单 #ORD2024003</div>
                                <div style="font-size: 12px; color: var(--text-200);">客户: 王先生 | 商品: 3种</div>
                            </div>
                            <span style="background: rgba(74, 222, 128, 0.2); color: #22c55e; padding: 4px 8px; border-radius: 8px; font-size: 12px; font-weight: 600;">已完成</span>
                        </div>
                        <div style="font-size: 12px; color: var(--text-200);">
                            完成时间: 14:25 | 用时: 12分钟
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-sort"></use></svg>
                            <span>分拣</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行：个人中心、会员中心、中控台、子会员管理 -->

        <!-- 个人中心 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, var(--accent-100), var(--accent-200)); margin: 0 auto 16px; display: flex; align-items: center; justify-content: center; box-shadow: 0 12px 24px rgba(113, 196, 239, 0.3);">
                            <svg class="icon" style="width: 40px; height: 40px; fill: white;">
                                <use href="#icon-profile"></use>
                            </svg>
                        </div>
                        <h2 style="margin-bottom: 8px; color: var(--text-100);">张三</h2>
                        <p style="color: var(--text-200); font-size: 14px;">高级分拣员 | ID: 001</p>
                    </div>

                    <!-- 个人统计 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 20px;">
                        <div class="glass-card" style="text-align: center; padding: 16px;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--accent-200); margin-bottom: 4px;">156</div>
                            <div style="color: var(--text-200); font-size: 12px;">本月分拣</div>
                        </div>
                        <div class="glass-card" style="text-align: center; padding: 16px;">
                            <div style="font-size: 24px; font-weight: 700; color: #4ade80; margin-bottom: 4px;">98.5%</div>
                            <div style="color: var(--text-200); font-size: 12px;">准确率</div>
                        </div>
                    </div>

                    <!-- 功能菜单 -->
                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <div style="display: flex; align-items: center;">
                                <svg class="icon" style="width: 20px; height: 20px; fill: var(--accent-200); margin-right: 12px;">
                                    <use href="#icon-analytics"></use>
                                </svg>
                                <span style="color: var(--text-100); font-size: 14px;">工作统计</span>
                            </div>
                            <span style="color: var(--text-200);">></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <div style="display: flex; align-items: center;">
                                <svg class="icon" style="width: 20px; height: 20px; fill: var(--accent-200); margin-right: 12px;">
                                    <use href="#icon-settings"></use>
                                </svg>
                                <span style="color: var(--text-100); font-size: 14px;">个人设置</span>
                            </div>
                            <span style="color: var(--text-200);">></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <div style="display: flex; align-items: center;">
                                <svg class="icon" style="width: 20px; height: 20px; fill: var(--accent-200); margin-right: 12px;">
                                    <use href="#icon-notification"></use>
                                </svg>
                                <span style="color: var(--text-100); font-size: 14px;">消息通知</span>
                            </div>
                            <span style="color: var(--text-200);">></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                            <div style="display: flex; align-items: center;">
                                <svg class="icon" style="width: 20px; height: 20px; fill: #ef4444; margin-right: 12px;">
                                    <use href="#icon-logout"></use>
                                </svg>
                                <span style="color: #ef4444; font-size: 14px;">退出登录</span>
                            </div>
                            <span style="color: var(--text-200);">></span>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 会员中心 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title" style="margin-bottom: 24px;">会员中心</h2>

                    <!-- 会员统计 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 8px; margin-bottom: 20px;">
                        <div class="glass-card" style="padding: 12px; text-align: center;">
                            <div style="font-size: 18px; font-weight: 700; color: var(--accent-200);">128</div>
                            <div style="font-size: 12px; color: var(--text-200);">总会员</div>
                        </div>
                        <div class="glass-card" style="padding: 12px; text-align: center;">
                            <div style="font-size: 18px; font-weight: 700; color: #4ade80;">95</div>
                            <div style="font-size: 12px; color: var(--text-200);">活跃会员</div>
                        </div>
                        <div class="glass-card" style="padding: 12px; text-align: center;">
                            <div style="font-size: 18px; font-weight: 700; color: #fbbf24;">12</div>
                            <div style="font-size: 12px; color: var(--text-200);">新增会员</div>
                        </div>
                    </div>

                    <!-- 功能模块 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">管理功能</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button style="padding: 12px; background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 12px; color: var(--accent-200); font-size: 14px; font-weight: 500;">会员列表</button>
                            <button style="padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #4ade80; border-radius: 12px; color: #22c55e; font-size: 14px; font-weight: 500;">新增会员</button>
                            <button style="padding: 12px; background: rgba(251, 191, 36, 0.1); border: 1px solid #fbbf24; border-radius: 12px; color: #f59e0b; font-size: 14px; font-weight: 500;">权限管理</button>
                            <button style="padding: 12px; background: rgba(168, 85, 247, 0.1); border: 1px solid #a855f7; border-radius: 12px; color: #9333ea; font-size: 14px; font-weight: 500;">数据统计</button>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">最近活动</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="background: rgba(113, 196, 239, 0.1); border: 1px solid rgba(113, 196, 239, 0.3); border-radius: 8px; padding: 8px;">
                                <div style="font-size: 12px; color: var(--text-100);">李四 完成了订单分拣</div>
                                <div style="font-size: 10px; color: var(--text-200);">2分钟前</div>
                            </div>
                            <div style="background: rgba(74, 222, 128, 0.1); border: 1px solid rgba(74, 222, 128, 0.3); border-radius: 8px; padding: 8px;">
                                <div style="font-size: 12px; color: var(--text-100);">王五 新增了采购订单</div>
                                <div style="font-size: 10px; color: var(--text-200);">5分钟前</div>
                            </div>
                            <div style="background: rgba(251, 191, 36, 0.1); border: 1px solid rgba(251, 191, 36, 0.3); border-radius: 8px; padding: 8px;">
                                <div style="font-size: 12px; color: var(--text-100);">赵六 更新了库存信息</div>
                                <div style="font-size: 10px; color: var(--text-200);">8分钟前</div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中控台页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title" style="margin-bottom: 24px;">中控台</h2>

                    <!-- 实时数据 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 20px;">
                        <div class="glass-card" style="padding: 16px;">
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <span class="status-dot status-online"></span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--text-100);">在线员工</span>
                            </div>
                            <div style="font-size: 24px; font-weight: 700; color: var(--accent-200);">24</div>
                            <div style="font-size: 12px; color: var(--text-200);">共32人</div>
                        </div>
                        <div class="glass-card" style="padding: 16px;">
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <span class="status-dot status-processing"></span>
                                <span style="font-size: 14px; font-weight: 600; color: var(--text-100);">处理中订单</span>
                            </div>
                            <div style="font-size: 24px; font-weight: 700; color: #fbbf24;">156</div>
                            <div style="font-size: 12px; color: var(--text-200);">今日总量</div>
                        </div>
                    </div>

                    <!-- 系统状态 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">系统状态</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">分拣系统</span>
                                <span style="background: rgba(74, 222, 128, 0.2); color: #22c55e; padding: 2px 8px; border-radius: 8px; font-size: 12px;">正常</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">库存管理</span>
                                <span style="background: rgba(74, 222, 128, 0.2); color: #22c55e; padding: 2px 8px; border-radius: 8px; font-size: 12px;">正常</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">订单系统</span>
                                <span style="background: rgba(251, 191, 36, 0.2); color: #f59e0b; padding: 2px 8px; border-radius: 8px; font-size: 12px;">维护中</span>
                            </div>
                        </div>
                    </div>

                    <!-- 今日概览 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">今日概览</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: var(--accent-200);">1,247</div>
                                <div style="font-size: 12px; color: var(--text-200);">订单处理</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #4ade80;">98.5%</div>
                                <div style="font-size: 12px; color: var(--text-200);">准确率</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #fbbf24;">12.5</div>
                                <div style="font-size: 12px; color: var(--text-200);">平均用时(分)</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #a855f7;">156</div>
                                <div style="font-size: 12px; color: var(--text-200);">异常处理</div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">快速操作</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="glow-btn" style="padding: 12px; font-size: 14px;">系统重启</button>
                            <button style="padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #4ade80; border-radius: 12px; color: #22c55e; font-size: 14px; font-weight: 500;">数据备份</button>
                            <button style="padding: 12px; background: rgba(251, 191, 36, 0.1); border: 1px solid #fbbf24; border-radius: 12px; color: #f59e0b; font-size: 14px; font-weight: 500;">性能监控</button>
                            <button style="padding: 12px; background: rgba(168, 85, 247, 0.1); border: 1px solid #a855f7; border-radius: 12px; color: #9333ea; font-size: 14px; font-weight: 500;">日志查看</button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 子会员管理页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title" style="margin-bottom: 24px;">子会员管理</h2>

                    <!-- 添加子会员按钮 -->
                    <div style="margin-bottom: 20px;">
                        <button class="glow-btn" style="width: 100%; padding: 12px; font-size: 16px;">
                            <svg class="icon" style="width: 16px; height: 16px; margin-right: 8px;">
                                <use href="#icon-member"></use>
                            </svg>
                            添加子会员
                        </button>
                    </div>

                    <!-- 子会员列表 -->
                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-100);">李四 (分拣员)</div>
                                <div style="font-size: 12px; color: var(--text-200);">ID: SUB001 | 手机: 138****1234</div>
                            </div>
                            <span class="status-dot status-online"></span>
                        </div>
                        <div style="font-size: 12px; color: var(--text-200); margin-bottom: 8px;">
                            权限: 分拣操作 | 本月分拣: 245单 | 绩效: A
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button style="padding: 4px 8px; background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 4px; color: var(--accent-200); font-size: 12px;">编辑</button>
                            <button style="padding: 4px 8px; background: rgba(251, 191, 36, 0.2); border: 1px solid #fbbf24; border-radius: 4px; color: #f59e0b; font-size: 12px;">权限</button>
                            <button style="padding: 4px 8px; background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; border-radius: 4px; color: #ef4444; font-size: 12px;">禁用</button>
                        </div>
                    </div>

                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-100);">王五 (入库员)</div>
                                <div style="font-size: 12px; color: var(--text-200);">ID: SUB002 | 手机: 139****2345</div>
                            </div>
                            <span class="status-dot status-online"></span>
                        </div>
                        <div style="font-size: 12px; color: var(--text-200); margin-bottom: 8px;">
                            权限: 入库管理 | 本月入库: 189单 | 绩效: B+
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button style="padding: 4px 8px; background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 4px; color: var(--accent-200); font-size: 12px;">编辑</button>
                            <button style="padding: 4px 8px; background: rgba(251, 191, 36, 0.2); border: 1px solid #fbbf24; border-radius: 4px; color: #f59e0b; font-size: 12px;">权限</button>
                            <button style="padding: 4px 8px; background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; border-radius: 4px; color: #ef4444; font-size: 12px;">禁用</button>
                        </div>
                    </div>

                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: 600; color: var(--text-100);">赵六 (财务)</div>
                                <div style="font-size: 12px; color: var(--text-200);">ID: SUB003 | 手机: 137****3456</div>
                            </div>
                            <span class="status-dot status-completed"></span>
                        </div>
                        <div style="font-size: 12px; color: var(--text-200); margin-bottom: 8px;">
                            权限: 财务管理 | 本月处理: 156笔 | 绩效: A+
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button style="padding: 4px 8px; background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 4px; color: var(--accent-200); font-size: 12px;">编辑</button>
                            <button style="padding: 4px 8px; background: rgba(251, 191, 36, 0.2); border: 1px solid #fbbf24; border-radius: 4px; color: #f59e0b; font-size: 12px;">权限</button>
                            <button style="padding: 4px 8px; background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; border-radius: 4px; color: #ef4444; font-size: 12px;">禁用</button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>子会员</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：横屏分拣界面、财务管理、数据分析、系统设置 -->

        <!-- 横屏分拣界面 -->
        <div class="landscape-frame">
            <div class="screen" style="padding: 20px;">
                <div style="display: flex; height: 100%;">
                    <!-- 左侧分类栏 -->
                    <div style="width: 120px; margin-right: 20px;">
                        <div style="background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 12px; padding: 8px; margin-bottom: 8px; text-align: center; color: var(--accent-200); font-size: 14px; font-weight: 600;">已分拣</div>
                        <div style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 12px; padding: 8px; margin-bottom: 8px; text-align: center; color: var(--text-100); font-size: 14px;">未分拣</div>
                        <div style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 12px; padding: 8px; margin-bottom: 8px; text-align: center; color: var(--text-100); font-size: 14px;">挂单</div>
                        <div style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 12px; padding: 8px; text-align: center; color: var(--text-100); font-size: 14px;">大厅</div>
                    </div>

                    <!-- 中间主要内容区 -->
                    <div style="flex: 1; margin-right: 20px;">
                        <!-- 标题栏 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <button style="padding: 8px 16px; background: linear-gradient(45deg, #ff9500, #ff6b00); border: none; border-radius: 8px; color: white; font-size: 14px; font-weight: 600;">挂单</button>
                            <h2 style="color: var(--text-100); font-size: 20px; font-weight: 700; margin: 0;">分拣作业 - 张先生订单</h2>
                            <button style="padding: 8px 16px; background: linear-gradient(45deg, #30d158, #28a745); border: none; border-radius: 8px; color: white; font-size: 14px; font-weight: 600;">分拣完成</button>
                        </div>

                        <!-- 商品网格 -->
                        <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 12px; height: calc(100% - 80px);">
                            <div class="glass-card" style="padding: 12px; text-align: center; background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100);">
                                <div style="font-size: 14px; font-weight: 600; color: var(--text-100);">蘑菇</div>
                                <div style="font-size: 12px; color: var(--text-200);">1箱</div>
                            </div>
                            <div class="glass-card" style="padding: 12px; text-align: center; background: rgba(74, 222, 128, 0.1); border: 1px solid #4ade80;">
                                <div style="font-size: 14px; font-weight: 600; color: var(--text-100);">西红柿</div>
                                <div style="font-size: 12px; color: var(--text-200);">3斤</div>
                            </div>
                            <div class="glass-card" style="padding: 12px; text-align: center; background: rgba(251, 191, 36, 0.1); border: 1px solid #fbbf24;">
                                <div style="font-size: 14px; font-weight: 600; color: var(--text-100);">黄瓜</div>
                                <div style="font-size: 12px; color: var(--text-200);">4斤</div>
                            </div>
                            <div class="glass-card" style="padding: 12px; text-align: center; background: rgba(168, 85, 247, 0.1); border: 1px solid #a855f7;">
                                <div style="font-size: 14px; font-weight: 600; color: var(--text-100);">胡萝卜</div>
                                <div style="font-size: 12px; color: var(--text-200);">2斤</div>
                            </div>
                            <div class="glass-card" style="padding: 12px; text-align: center; background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444;">
                                <div style="font-size: 14px; font-weight: 600; color: var(--text-100);">小白菜</div>
                                <div style="font-size: 12px; color: var(--text-200);">6斤</div>
                            </div>
                            <div class="glass-card" style="padding: 12px; text-align: center; background: rgba(74, 222, 128, 0.1); border: 1px solid #4ade80;">
                                <div style="font-size: 14px; font-weight: 600; color: var(--text-100);">豆腐</div>
                                <div style="font-size: 12px; color: var(--text-200);">2盒</div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧操作面板 -->
                    <div style="width: 200px;">
                        <div class="glass-card" style="padding: 16px; margin-bottom: 16px;">
                            <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">订单信息</h4>
                            <div style="margin-bottom: 8px;">
                                <span style="color: var(--text-200); font-size: 12px;">客户：</span>
                                <span style="color: var(--text-100); font-size: 14px; font-weight: 600;">张先生</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <span style="color: var(--text-200); font-size: 12px;">电话：</span>
                                <span style="color: var(--text-100); font-size: 14px;">138****5678</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <span style="color: var(--text-200); font-size: 12px;">地址：</span>
                                <span style="color: var(--text-100); font-size: 14px;">朝阳区xxx小区</span>
                            </div>
                        </div>

                        <div class="glass-card" style="padding: 16px;">
                            <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">分拣进度</h4>
                            <div style="background: var(--bg-200); border-radius: 8px; height: 8px; margin-bottom: 8px;">
                                <div style="background: linear-gradient(90deg, var(--accent-100), var(--accent-200)); height: 100%; width: 75%; border-radius: 8px;"></div>
                            </div>
                            <div style="text-align: center; color: var(--accent-200); font-size: 18px; font-weight: 700;">75%</div>
                            <div style="text-align: center; color: var(--text-200); font-size: 12px;">4/6 已完成</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 财务管理页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title" style="margin-bottom: 24px;">财务管理</h2>

                    <!-- 财务概览 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 20px;">
                        <div class="glass-card" style="padding: 16px; text-align: center;">
                            <div style="font-size: 20px; font-weight: 700; color: #4ade80; margin-bottom: 4px;">¥156,789</div>
                            <div style="color: var(--text-200); font-size: 12px;">本月收入</div>
                        </div>
                        <div class="glass-card" style="padding: 16px; text-align: center;">
                            <div style="font-size: 20px; font-weight: 700; color: #ef4444; margin-bottom: 4px;">¥89,456</div>
                            <div style="color: var(--text-200); font-size: 12px;">本月支出</div>
                        </div>
                    </div>

                    <!-- 收支明细 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">最近交易</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(74, 222, 128, 0.1); border-radius: 8px;">
                                <div>
                                    <div style="font-size: 14px; color: var(--text-100); font-weight: 600;">订单收入</div>
                                    <div style="font-size: 12px; color: var(--text-200);">2024-03-15 14:30</div>
                                </div>
                                <div style="color: #4ade80; font-size: 16px; font-weight: 700;">+¥2,580</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(239, 68, 68, 0.1); border-radius: 8px;">
                                <div>
                                    <div style="font-size: 14px; color: var(--text-100); font-weight: 600;">设备维护</div>
                                    <div style="font-size: 12px; color: var(--text-200);">2024-03-15 10:15</div>
                                </div>
                                <div style="color: #ef4444; font-size: 16px; font-weight: 700;">-¥1,200</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(74, 222, 128, 0.1); border-radius: 8px;">
                                <div>
                                    <div style="font-size: 14px; color: var(--text-100); font-weight: 600;">分拣服务费</div>
                                    <div style="font-size: 12px; color: var(--text-200);">2024-03-14 16:45</div>
                                </div>
                                <div style="color: #4ade80; font-size: 16px; font-weight: 700;">+¥3,200</div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计图表 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 16px;">收支趋势</h4>
                        <div class="chart-container">
                            <svg width="100%" height="120" viewBox="0 0 300 120">
                                <defs>
                                    <linearGradient id="incomeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#4ade80;stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:#4ade80;stop-opacity:0" />
                                    </linearGradient>
                                </defs>
                                <path class="chart-line" d="M20,90 L80,70 L140,50 L200,40 L260,30" stroke="#4ade80" stroke-width="3" fill="none" />
                                <path d="M20,90 L80,70 L140,50 L200,40 L260,30 L260,100 L20,100 Z" fill="url(#incomeGradient)" />
                                <circle cx="20" cy="90" r="4" fill="#4ade80" />
                                <circle cx="80" cy="70" r="4" fill="#4ade80" />
                                <circle cx="140" cy="50" r="4" fill="#4ade80" />
                                <circle cx="200" cy="40" r="4" fill="#4ade80" />
                                <circle cx="260" cy="30" r="4" fill="#4ade80" />
                            </svg>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 12px; color: var(--text-200);">
                            <span>1月</span>
                            <span>2月</span>
                            <span>3月</span>
                            <span>4月</span>
                            <span>5月</span>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">快速操作</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="glow-btn" style="padding: 12px; font-size: 14px;">记录收入</button>
                            <button style="padding: 12px; background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 12px; color: #ef4444; font-size: 14px; font-weight: 500;">记录支出</button>
                            <button style="padding: 12px; background: rgba(251, 191, 36, 0.1); border: 1px solid #fbbf24; border-radius: 12px; color: #f59e0b; font-size: 14px; font-weight: 500;">生成报表</button>
                            <button style="padding: 12px; background: rgba(168, 85, 247, 0.1); border: 1px solid #a855f7; border-radius: 12px; color: #9333ea; font-size: 14px; font-weight: 500;">导出数据</button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>财务</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据分析页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title" style="margin-bottom: 24px;">数据分析</h2>

                    <!-- 核心指标 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 20px;">
                        <div class="glass-card" style="padding: 16px; text-align: center;">
                            <div style="font-size: 20px; font-weight: 700; color: var(--accent-200); margin-bottom: 4px;">1,247</div>
                            <div style="color: var(--text-200); font-size: 12px;">今日订单</div>
                        </div>
                        <div class="glass-card" style="padding: 16px; text-align: center;">
                            <div style="font-size: 20px; font-weight: 700; color: #4ade80; margin-bottom: 4px;">98.5%</div>
                            <div style="color: var(--text-200); font-size: 12px;">准确率</div>
                        </div>
                    </div>

                    <!-- 趋势图表 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 16px;">订单处理趋势</h4>
                        <div class="chart-container">
                            <svg width="100%" height="120" viewBox="0 0 300 120">
                                <defs>
                                    <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:var(--accent-100);stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:var(--accent-100);stop-opacity:0" />
                                    </linearGradient>
                                </defs>
                                <path class="chart-line" d="M20,80 L60,70 L100,60 L140,45 L180,50 L220,35 L260,30" stroke="var(--accent-200)" stroke-width="3" fill="none" />
                                <path d="M20,80 L60,70 L100,60 L140,45 L180,50 L220,35 L260,30 L260,100 L20,100 Z" fill="url(#trendGradient)" />
                                <circle cx="20" cy="80" r="4" fill="var(--accent-100)" />
                                <circle cx="60" cy="70" r="4" fill="var(--accent-100)" />
                                <circle cx="100" cy="60" r="4" fill="var(--accent-100)" />
                                <circle cx="140" cy="45" r="4" fill="var(--accent-100)" />
                                <circle cx="180" cy="50" r="4" fill="var(--accent-100)" />
                                <circle cx="220" cy="35" r="4" fill="var(--accent-100)" />
                                <circle cx="260" cy="30" r="4" fill="var(--accent-100)" />
                            </svg>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 12px; color: var(--text-200);">
                            <span>周一</span>
                            <span>周二</span>
                            <span>周三</span>
                            <span>周四</span>
                            <span>周五</span>
                            <span>周六</span>
                            <span>周日</span>
                        </div>
                    </div>

                    <!-- 分类统计 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">商品分类统计</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">生鲜蔬菜</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 60px; height: 6px; background: var(--bg-200); border-radius: 3px;">
                                        <div style="width: 75%; height: 100%; background: var(--accent-200); border-radius: 3px;"></div>
                                    </div>
                                    <span style="color: var(--text-200); font-size: 12px;">75%</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">肉类水产</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 60px; height: 6px; background: var(--bg-200); border-radius: 3px;">
                                        <div style="width: 60%; height: 100%; background: #4ade80; border-radius: 3px;"></div>
                                    </div>
                                    <span style="color: var(--text-200); font-size: 12px;">60%</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">日用百货</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 60px; height: 6px; background: var(--bg-200); border-radius: 3px;">
                                        <div style="width: 45%; height: 100%; background: #fbbf24; border-radius: 3px;"></div>
                                    </div>
                                    <span style="color: var(--text-200); font-size: 12px;">45%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 效率分析 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">效率分析</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: 700; color: var(--accent-200);">12.5分</div>
                                <div style="font-size: 12px; color: var(--text-200);">平均处理时间</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: 700; color: #4ade80;">156件</div>
                                <div style="font-size: 12px; color: var(--text-200);">每小时处理量</div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统设置页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title" style="margin-bottom: 24px;">系统设置</h2>

                    <!-- 账户信息 -->
                    <div class="glass-card">
                        <div style="display: flex; align-items: center; margin-bottom: 16px;">
                            <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--accent-100), var(--accent-200)); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <svg class="icon" style="width: 30px; height: 30px; fill: white;">
                                    <use href="#icon-profile"></use>
                                </svg>
                            </div>
                            <div>
                                <h3 style="color: var(--text-100); font-size: 18px; font-weight: 600; margin-bottom: 4px;">管理员</h3>
                                <p style="color: var(--text-200); font-size: 14px;"><EMAIL></p>
                            </div>
                        </div>
                        <button class="glow-btn" style="width: 100%; padding: 12px; font-size: 14px;">编辑个人资料</button>
                    </div>

                    <!-- 系统配置 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 16px;">系统配置</h4>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <div>
                                <div style="color: var(--text-100); font-size: 14px; font-weight: 500;">自动分拣</div>
                                <div style="color: var(--text-200); font-size: 12px;">启用AI智能分拣功能</div>
                            </div>
                            <div style="width: 44px; height: 24px; background: var(--accent-100); border-radius: 12px; position: relative; cursor: pointer;">
                                <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <div>
                                <div style="color: var(--text-100); font-size: 14px; font-weight: 500;">异常提醒</div>
                                <div style="color: var(--text-200); font-size: 12px;">分拣异常时自动通知</div>
                            </div>
                            <div style="width: 44px; height: 24px; background: var(--accent-100); border-radius: 12px; position: relative; cursor: pointer;">
                                <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="color: var(--text-100); font-size: 14px; font-weight: 500;">数据同步</div>
                                <div style="color: var(--text-200); font-size: 12px;">实时同步分拣数据</div>
                            </div>
                            <div style="width: 44px; height: 24px; background: var(--bg-300); border-radius: 12px; position: relative; cursor: pointer;">
                                <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.3s ease;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 设备管理 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 16px;">设备管理</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div style="text-align: center; padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #22c55e; border-radius: 12px;">
                                <div style="color: #22c55e; font-size: 18px; font-weight: 700;">5</div>
                                <div style="color: var(--text-200); font-size: 12px;">在线设备</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 12px;">
                                <div style="color: #ef4444; font-size: 18px; font-weight: 700;">1</div>
                                <div style="color: var(--text-200); font-size: 12px;">离线设备</div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他设置 -->
                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; cursor: pointer;">
                            <span style="color: var(--text-100); font-size: 14px; font-weight: 500;">数据备份</span>
                            <span style="color: var(--text-200); font-size: 18px;">→</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; cursor: pointer;">
                            <span style="color: var(--text-100); font-size: 14px; font-weight: 500;">权限管理</span>
                            <span style="color: var(--text-200); font-size: 18px;">→</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; cursor: pointer;">
                            <span style="color: var(--text-100); font-size: 14px; font-weight: 500;">系统日志</span>
                            <span style="color: var(--text-200); font-size: 18px;">→</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; cursor: pointer;">
                            <span style="color: var(--text-100); font-size: 14px; font-weight: 500;">帮助中心</span>
                            <span style="color: var(--text-200); font-size: 18px;">→</span>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-inventory"></use></svg>
                            <span>库存</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：添加商品页面、设备监控页面、报表页面、横屏分拣界面 -->

        <!-- 添加商品页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="display: flex; align-items: center; margin-bottom: 24px;">
                        <button style="background: none; border: none; color: var(--accent-200); font-size: 18px; margin-right: 12px;">←</button>
                        <h2 style="color: var(--text-100); font-size: 20px; font-weight: 600; margin: 0;">添加商品</h2>
                    </div>

                    <!-- 商品信息表单 -->
                    <div class="glass-card">
                        <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">商品名称 *</label>
                        <input type="text" class="input-field" placeholder="输入商品名称" value="有机西红柿">
                    </div>

                    <div class="glass-card">
                        <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">商品编码 *</label>
                        <input type="text" class="input-field" placeholder="输入商品编码" value="VEG001">
                    </div>

                    <div class="glass-card">
                        <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">商品分类</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button style="padding: 12px; background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 12px; color: var(--accent-200); font-size: 14px; font-weight: 500;">生鲜蔬菜</button>
                            <button style="padding: 12px; background: transparent; border: 1px solid var(--bg-300); border-radius: 12px; color: var(--text-200); font-size: 14px; font-weight: 500;">肉类水产</button>
                            <button style="padding: 12px; background: transparent; border: 1px solid var(--bg-300); border-radius: 12px; color: var(--text-200); font-size: 14px; font-weight: 500;">日用百货</button>
                            <button style="padding: 12px; background: transparent; border: 1px solid var(--bg-300); border-radius: 12px; color: var(--text-200); font-size: 14px; font-weight: 500;">冷冻食品</button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                        <div class="glass-card">
                            <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">单价</label>
                            <input type="number" class="input-field" placeholder="0.00" value="12.50" style="color: var(--text-100);">
                        </div>
                        <div class="glass-card">
                            <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">库存</label>
                            <input type="number" class="input-field" placeholder="0" value="500" style="color: var(--text-100);">
                        </div>
                    </div>

                    <div class="glass-card">
                        <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">商品图片</label>
                        <div style="border: 2px dashed var(--bg-300); border-radius: 12px; padding: 24px; text-align: center; background: rgba(255, 255, 255, 0.1);">
                            <svg class="icon" style="width: 32px; height: 32px; fill: var(--text-200); margin-bottom: 8px;">
                                <use href="#icon-inventory"></use>
                            </svg>
                            <p style="color: var(--text-200); font-size: 14px; margin: 0;">点击上传商品图片</p>
                        </div>
                    </div>

                    <!-- 分拣规则 -->
                    <div class="glass-card">
                        <label style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 12px; display: block;">分拣规则</label>
                        <div style="background: rgba(113, 196, 239, 0.1); border: 1px solid rgba(113, 196, 239, 0.3); border-radius: 12px; padding: 12px; margin-bottom: 8px;">
                            <p style="color: var(--text-100); font-size: 14px; margin: 0;">按重量分拣：小于200g → 通道A，200-500g → 通道B，大于500g → 通道C</p>
                        </div>
                        <button style="width: 100%; padding: 8px; background: transparent; border: 1px dashed var(--accent-100); border-radius: 8px; color: var(--accent-200); font-size: 14px;">+ 添加分拣规则</button>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-top: 24px;">
                        <button style="padding: 16px; background: transparent; border: 1px solid var(--bg-300); border-radius: 16px; color: var(--text-200); font-size: 16px; font-weight: 600;">取消</button>
                        <button class="glow-btn" style="padding: 16px; font-size: 16px; font-weight: 600;">添加商品</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备监控页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="display: flex; align-items: center; margin-bottom: 24px;">
                        <button style="background: none; border: none; color: var(--accent-200); font-size: 18px; margin-right: 12px;">←</button>
                        <h2 style="color: var(--text-100); font-size: 20px; font-weight: 600; margin: 0;">设备监控</h2>
                    </div>

                    <!-- 设备状态概览 -->
                    <div class="glass-card" style="text-align: center;">
                        <div style="color: var(--text-200); font-size: 14px; margin-bottom: 8px;">设备运行状态</div>
                        <div style="font-size: 32px; font-weight: 700; color: #4ade80; margin-bottom: 8px;">5/6</div>
                        <div style="background: var(--bg-200); border-radius: 8px; height: 6px; margin-bottom: 8px;">
                            <div style="background: linear-gradient(90deg, #4ade80, #22c55e); height: 100%; width: 83%; border-radius: 8px;"></div>
                        </div>
                        <div style="color: var(--text-200); font-size: 12px;">1台设备离线</div>
                    </div>

                    <!-- 设备列表 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 16px;">设备列表</h4>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #22c55e; border-radius: 12px; margin-bottom: 8px;">
                            <div>
                                <div style="color: var(--text-100); font-size: 14px; font-weight: 500;">分拣机A</div>
                                <div style="color: var(--text-200); font-size: 12px;">ID: DEV001</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="color: #22c55e; font-size: 12px; font-weight: 600;">在线</div>
                                <div style="color: var(--text-200); font-size: 12px;">效率: 98%</div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #22c55e; border-radius: 12px; margin-bottom: 8px;">
                            <div>
                                <div style="color: var(--text-100); font-size: 14px; font-weight: 500;">分拣机B</div>
                                <div style="color: var(--text-200); font-size: 12px;">ID: DEV002</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="color: #22c55e; font-size: 12px; font-weight: 600;">在线</div>
                                <div style="color: var(--text-200); font-size: 12px;">效率: 95%</div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 12px; margin-bottom: 8px;">
                            <div>
                                <div style="color: var(--text-100); font-size: 14px; font-weight: 500;">分拣机C</div>
                                <div style="color: var(--text-200); font-size: 12px;">ID: DEV003</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="color: #ef4444; font-size: 12px; font-weight: 600;">离线</div>
                                <div style="color: var(--text-200); font-size: 12px;">故障</div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时数据 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">实时数据</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div style="text-align: center; padding: 12px; background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 12px;">
                                <div style="color: var(--accent-200); font-size: 18px; font-weight: 700;">156</div>
                                <div style="color: var(--text-200); font-size: 12px;">每小时处理</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #22c55e; border-radius: 12px;">
                                <div style="color: #22c55e; font-size: 18px; font-weight: 700;">98.5%</div>
                                <div style="color: var(--text-200); font-size: 12px;">准确率</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-top: 24px;">
                        <button style="padding: 16px; background: rgba(251, 191, 36, 0.2); border: 1px solid #f59e0b; border-radius: 16px; color: #f59e0b; font-size: 16px; font-weight: 600;">重启设备</button>
                        <button class="glow-btn" style="padding: 16px; font-size: 16px; font-weight: 600;">刷新状态</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-inventory"></use></svg>
                            <span>库存</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>监控</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="display: flex; align-items: center; margin-bottom: 24px;">
                        <button style="background: none; border: none; color: var(--accent-200); font-size: 18px; margin-right: 12px;">←</button>
                        <h2 style="color: var(--text-100); font-size: 20px; font-weight: 600; margin: 0;">数据报表</h2>
                    </div>

                    <!-- 时间选择 -->
                    <div class="glass-card">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="color: var(--text-100); font-size: 18px; font-weight: 600;">2024年3月</div>
                                <div style="color: var(--text-200); font-size: 14px;">月度报表</div>
                            </div>
                            <button style="padding: 8px 16px; background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 12px; color: var(--accent-200); font-size: 14px;">选择时间</button>
                        </div>
                    </div>

                    <!-- 核心数据 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 16px;">核心数据</h4>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
                            <div style="text-align: center; padding: 12px; background: rgba(113, 196, 239, 0.1); border: 1px solid var(--accent-100); border-radius: 12px;">
                                <div style="color: var(--accent-200); font-size: 18px; font-weight: 700;">38,247</div>
                                <div style="color: var(--text-200); font-size: 12px;">总订单数</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(74, 222, 128, 0.1); border: 1px solid #22c55e; border-radius: 12px;">
                                <div style="color: #22c55e; font-size: 18px; font-weight: 700;">98.2%</div>
                                <div style="color: var(--text-200); font-size: 12px;">平均准确率</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div style="text-align: center; padding: 12px; background: rgba(251, 191, 36, 0.1); border: 1px solid #f59e0b; border-radius: 12px;">
                                <div style="color: #f59e0b; font-size: 18px; font-weight: 700;">156</div>
                                <div style="color: var(--text-200); font-size: 12px;">每小时处理</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(168, 85, 247, 0.1); border: 1px solid #a855f7; border-radius: 12px;">
                                <div style="color: #a855f7; font-size: 18px; font-weight: 700;">12.5分</div>
                                <div style="color: var(--text-200); font-size: 12px;">平均处理时间</div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类统计 -->
                    <div class="glass-card">
                        <h4 style="color: var(--text-100); font-size: 16px; font-weight: 600; margin-bottom: 12px;">商品分类统计</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">生鲜蔬菜</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 60px; height: 6px; background: var(--bg-200); border-radius: 3px;">
                                        <div style="width: 75%; height: 100%; background: var(--accent-200); border-radius: 3px;"></div>
                                    </div>
                                    <span style="color: var(--text-200); font-size: 12px;">28,685</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">肉类水产</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 60px; height: 6px; background: var(--bg-200); border-radius: 3px;">
                                        <div style="width: 60%; height: 100%; background: #4ade80; border-radius: 3px;"></div>
                                    </div>
                                    <span style="color: var(--text-200); font-size: 12px;">6,892</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: var(--text-100); font-size: 14px;">日用百货</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 60px; height: 6px; background: var(--bg-200); border-radius: 3px;">
                                        <div style="width: 45%; height: 100%; background: #fbbf24; border-radius: 3px;"></div>
                                    </div>
                                    <span style="color: var(--text-200); font-size: 12px;">2,670</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-top: 24px;">
                        <button style="padding: 16px; background: rgba(74, 222, 128, 0.2); border: 1px solid #22c55e; border-radius: 16px; color: #22c55e; font-size: 16px; font-weight: 600;">导出报表</button>
                        <button class="glow-btn" style="padding: 16px; font-size: 16px; font-weight: 600;">生成报告</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-inventory"></use></svg>
                            <span>库存</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>报表</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 横屏分拣界面 -->
        <div class="landscape-frame">
            <div class="screen" style="padding: 20px;">
                <div style="display: flex; height: 100%;">
                    <!-- 左侧商品分类 -->
                    <div style="width: 200px; margin-right: 20px;">
                        <h2 style="color: var(--text-100); font-size: 20px; font-weight: 700; margin-bottom: 20px;">商品分类</h2>

                        <div class="glass-card" style="margin-bottom: 16px; padding: 16px;">
                            <h4 style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 12px;">生鲜蔬菜</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <button class="glow-btn" style="padding: 8px 12px; font-size: 12px;">西红柿</button>
                                <button style="padding: 8px 12px; background: rgba(74, 222, 128, 0.2); border: 1px solid #22c55e; border-radius: 8px; color: #22c55e; font-size: 12px;">黄瓜</button>
                                <button style="padding: 8px 12px; background: rgba(251, 191, 36, 0.2); border: 1px solid #f59e0b; border-radius: 8px; color: #f59e0b; font-size: 12px;">胡萝卜</button>
                            </div>
                        </div>

                        <div class="glass-card" style="padding: 16px;">
                            <h4 style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 12px;">肉类水产</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <button style="padding: 8px 12px; background: rgba(168, 85, 247, 0.2); border: 1px solid #a855f7; border-radius: 8px; color: #a855f7; font-size: 12px;">猪肉</button>
                                <button style="padding: 8px 12px; background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; border-radius: 8px; color: #ef4444; font-size: 12px;">鱼类</button>
                            </div>
                        </div>
                    </div>

                    <!-- 中间分拣区域 -->
                    <div style="flex: 1; background: rgba(255, 255, 255, 0.3); border: 2px dashed var(--bg-300); border-radius: 20px; position: relative; overflow: hidden;">
                        <!-- 分拣通道 -->
                        <div style="position: absolute; top: 20px; left: 20px; width: 150px; height: 100px; background: rgba(113, 196, 239, 0.2); border: 2px solid var(--accent-100); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <div style="text-align: center;">
                                <div style="color: var(--accent-200); font-size: 16px; font-weight: 700;">通道A</div>
                                <div style="color: var(--text-200); font-size: 12px;">小件商品</div>
                            </div>
                        </div>

                        <div style="position: absolute; top: 20px; right: 20px; width: 150px; height: 100px; background: rgba(74, 222, 128, 0.2); border: 2px solid #22c55e; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <div style="text-align: center;">
                                <div style="color: #22c55e; font-size: 16px; font-weight: 700;">通道B</div>
                                <div style="color: var(--text-200); font-size: 12px;">中件商品</div>
                            </div>
                        </div>

                        <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); width: 150px; height: 100px; background: rgba(251, 191, 36, 0.2); border: 2px solid #f59e0b; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <div style="text-align: center;">
                                <div style="color: #f59e0b; font-size: 16px; font-weight: 700;">通道C</div>
                                <div style="color: var(--text-200); font-size: 12px;">大件商品</div>
                            </div>
                        </div>

                        <!-- 传送带指示 -->
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 4px; background: linear-gradient(90deg, var(--accent-100), #22c55e, #f59e0b); border-radius: 2px; animation: conveyor 3s linear infinite;"></div>
                    </div>

                    <!-- 右侧操作面板 -->
                    <div style="width: 180px; margin-left: 20px;">
                        <div class="glass-card" style="padding: 16px; margin-bottom: 16px;">
                            <h4 style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 12px;">分拣状态</h4>
                            <div style="margin-bottom: 12px;">
                                <div style="color: var(--text-200); font-size: 12px; margin-bottom: 4px;">当前速度</div>
                                <div style="color: var(--accent-200); font-size: 18px; font-weight: 700;">156件/小时</div>
                            </div>
                            <div style="margin-bottom: 12px;">
                                <div style="color: var(--text-200); font-size: 12px; margin-bottom: 4px;">准确率</div>
                                <div style="color: #22c55e; font-size: 18px; font-weight: 700;">98.5%</div>
                            </div>
                            <div>
                                <div style="color: var(--text-200); font-size: 12px; margin-bottom: 4px;">今日处理</div>
                                <div style="color: #f59e0b; font-size: 18px; font-weight: 700;">1,247件</div>
                            </div>
                        </div>

                        <div class="glass-card" style="padding: 16px;">
                            <h4 style="color: var(--text-100); font-size: 14px; font-weight: 600; margin-bottom: 12px;">操作</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <button style="padding: 8px 12px; background: rgba(113, 196, 239, 0.2); border: 1px solid var(--accent-100); border-radius: 8px; color: var(--accent-200); font-size: 12px;">开始分拣</button>
                                <button style="padding: 8px 12px; background: rgba(251, 191, 36, 0.2); border: 1px solid #f59e0b; border-radius: 8px; color: #f59e0b; font-size: 12px;">暂停</button>
                                <button style="padding: 8px 12px; background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; border-radius: 8px; color: #ef4444; font-size: 12px;">停止</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
