<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣系统 - 科技感UI设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: auto;
            padding: 20px;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            max-width: 2400px;
            margin: 0 auto;
            justify-content: center;
        }

        .container > .phone-frame:not(.landscape-page) {
            flex: 0 0 375px;
        }

        .container > .phone-frame.landscape-page {
            flex: 0 0 812px;
            width: 812px;
            height: 375px;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #333;
            border-radius: 40px;
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .screen {
            width: 100%;
            height: 100%;
            border-radius: 35px;
            overflow: hidden;
            position: relative;
        }

        .page {
            width: 100%;
            height: 100%;
            padding: 20px;
            background: linear-gradient(145deg, #0f0f23, #1a1a2e);
            position: relative;
        }

        /* 玻璃拟态效果 */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }

        /* 发光按钮 */
        .glow-btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }

        .glow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 25px rgba(0, 212, 255, 0.5);
        }

        /* 标题样式 */
        .page-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(45deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
        }

        /* 输入框样式 */
        .input-field {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            margin-bottom: 16px;
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* 状态指示器 */
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-pending { background: #ff9500; }
        .status-processing { background: #00d4ff; }
        .status-completed { background: #30d158; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 图标样式 */
        .icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        /* 导航栏 */
        .nav-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #00d4ff;
        }

        /* 动态折线图 */
        .chart-line {
            stroke: #00d4ff;
            stroke-width: 2;
            fill: none;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 3s ease-in-out forwards;
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        /* 粒子效果背景 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00d4ff;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 悬浮语音按钮 */
        .voice-btn {
            position: absolute;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff0080, #ff4081);
            border: none;
            box-shadow: 0 4px 20px rgba(255, 0, 128, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            animation: voicePulse 2s infinite;
        }

        @keyframes voicePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* 表格样式 */
        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .order-table th,
        .order-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .order-table th {
            background: rgba(0, 212, 255, 0.2);
            font-weight: 600;
            color: #00d4ff;
        }

        /* 横屏分拣页面 */
        .landscape-page {
            width: 812px;
            height: 375px;
            display: flex;
        }

        .sidebar {
            width: 100px;
            background: rgba(0, 0, 0, 0.8);
            padding: 12px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sidebar-item {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .sidebar-item.active {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
        }

        .main-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .product-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
        }

        .product-card.completed {
            border-color: #30d158;
            background: rgba(48, 209, 88, 0.1);
        }


    </style>
</head>
<body>
    <!-- SVG图标定义 -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-login" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17C12.8 17 13.5 16.3 13.5 15.5S12.8 14 12 14 10.5 14.7 10.5 15.5 11.2 17 12 17Z"/>
            </symbol>
            <symbol id="icon-scan" viewBox="0 0 24 24">
                <path d="M9,2V5H7V4A1,1 0 0,0 6,3H3A1,1 0 0,0 2,4V7A1,1 0 0,0 3,8H4V6H7V9H2V15H7V12H9V15A1,1 0 0,0 10,16H13A1,1 0 0,0 14,15V12H17V15H20V9H15V12H13V9A1,1 0 0,0 12,8H9A1,1 0 0,0 8,9V12H6V6H9V2M15,2V5H17V4A1,1 0 0,1 18,3H21A1,1 0 0,1 22,4V7A1,1 0 0,1 21,8H20V6H17V9H22V15H17V12H15V15A1,1 0 0,1 14,16H11A1,1 0 0,1 10,15V12H8V15H5V9H10V12H12V9A1,1 0 0,1 13,8H16A1,1 0 0,1 17,9V12H19V6H16V2H15Z"/>
            </symbol>
            <symbol id="icon-order" viewBox="0 0 24 24">
                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
            </symbol>
            <symbol id="icon-sort" viewBox="0 0 24 24">
                <path d="M9,3L5,7H8V14H10V7H13M16,17V10H14V17H11L15,21L19,17H16Z"/>
            </symbol>
            <symbol id="icon-profile" viewBox="0 0 24 24">
                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
            </symbol>
            <symbol id="icon-member" viewBox="0 0 24 24">
                <path d="M16,4C18.2,4 20,5.8 20,8C20,10.2 18.2,12 16,12C13.8,12 12,10.2 12,8C12,5.8 13.8,4 16,4M16,14C20.4,14 24,15.8 24,18V20H8V18C8,15.8 11.6,14 16,14M8.5,4C10.4,4 12,5.6 12,7.5C12,9.4 10.4,11 8.5,11C6.6,11 5,9.4 5,7.5C5,5.6 6.6,4 8.5,4M8.5,13C11.5,13 14,14.2 14,15.8V17H3V15.8C3,14.2 5.5,13 8.5,13Z"/>
            </symbol>
            <symbol id="icon-dashboard" viewBox="0 0 24 24">
                <path d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/>
            </symbol>
            <symbol id="icon-finance" viewBox="0 0 24 24">
                <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
            </symbol>
            <symbol id="icon-sale" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
            </symbol>
            <symbol id="icon-analytics" viewBox="0 0 24 24">
                <path d="M22,21H2V3H4V19H6V17H10V19H12V16H16V19H18V17H22V21Z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <!-- 登录页 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div class="particles">
                        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                        <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
                        <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
                        <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
                        <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 100px;">
                        <svg class="icon" style="width: 80px; height: 80px; margin-bottom: 30px;">
                            <use href="#icon-login"></use>
                        </svg>
                        <h1 class="page-title">AI智能分拣系统</h1>
                        <p style="color: rgba(255,255,255,0.7); margin-bottom: 40px;">科技赋能，智慧分拣</p>
                    </div>

                    <div class="glass" style="padding: 30px; margin: 40px 0;">
                        <input type="text" class="input-field" placeholder="请输入会员账号">
                        <input type="password" class="input-field" placeholder="请输入密码">
                        
                        <button class="glow-btn" style="width: 100%; margin-top: 20px;">
                            登录系统
                        </button>
                        
                        <div style="text-align: center; margin-top: 20px;">
                            <a href="#" style="color: #00d4ff; text-decoration: none;">忘记密码？</a>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 40px;">
                        <p style="color: rgba(255,255,255,0.5); font-size: 12px;">
                            权限验证 • 模块检测 • 安全登录
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 入库模块 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">商品入库</h2>

                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">未入库</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">已入库</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024001</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 新鲜蔬菜批发</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 8种 | 预计到货: 今日14:00
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始入库</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024002</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 优质肉类配送</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 5种 | 预计到货: 今日16:30
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始入库</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024003</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 海鲜直供</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 3种 | 预计到货: 明日09:00
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始入库</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 入库详情页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">入库详情</h2>

                    <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">采购单 #PO2024001</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">供应商: 新鲜蔬菜批发</div>
                            </div>
                            <span style="color: #00d4ff; font-size: 12px;">进行中</span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            到货时间: 2024-07-28 14:00 | 操作员: 张三
                        </div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3>商品清单</h3>
                            <div style="display: flex; gap: 8px;">
                                <button class="glow-btn" style="padding: 6px 12px; font-size: 12px; background: linear-gradient(45deg, #00d4ff, #0099cc);">增加</button>
                                <button class="glow-btn" style="padding: 6px 12px; font-size: 12px; background: linear-gradient(45deg, #30d158, #28a745);">完成</button>
                            </div>
                        </div>
                        <table class="order-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>预计</th>
                                    <th>实际</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>大白菜</td>
                                    <td>50斤</td>
                                    <td style="color: #30d158;">50斤</td>
                                    <td><span style="color: #30d158; font-size: 12px;">✓</span></td>
                                </tr>
                                <tr>
                                    <td>小白菜</td>
                                    <td>30斤</td>
                                    <td style="color: #30d158;">30斤</td>
                                    <td><span style="color: #30d158; font-size: 12px;">✓</span></td>
                                </tr>
                                <tr>
                                    <td>西红柿</td>
                                    <td>25斤</td>
                                    <td style="color: #00d4ff;">20斤</td>
                                    <td><span style="color: #ff9500; font-size: 12px;">进行中</span></td>
                                </tr>
                                <tr>
                                    <td>黄瓜</td>
                                    <td>20斤</td>
                                    <td style="color: rgba(255,255,255,0.4);">-</td>
                                    <td><span style="color: rgba(255,255,255,0.4); font-size: 12px;">待入库</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 悬浮语音按钮 -->
                    <button class="voice-btn">
                        <svg style="width: 24px; height: 24px; fill: white;" viewBox="0 0 24 24">
                            <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
                        </svg>
                    </button>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单录入 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">订单录入</h2>

                    <div class="card" style="margin-bottom: 16px;">
                        <input type="text" class="input-field" placeholder="客户姓名" style="margin-bottom: 0;">
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3>商品清单</h3>
                            <button class="glow-btn" style="padding: 8px 16px; font-size: 14px;">确定</button>
                        </div>
                        <table class="order-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>数量</th>
                                    <th>单位</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>大白菜</td>
                                    <td>5</td>
                                    <td>斤</td>
                                </tr>
                                <tr>
                                    <td>小白菜</td>
                                    <td>6</td>
                                    <td>斤</td>
                                </tr>
                                <tr>
                                    <td>蘑菇</td>
                                    <td>1</td>
                                    <td>箱</td>
                                </tr>
                                <tr>
                                    <td>西红柿</td>
                                    <td>3</td>
                                    <td>斤</td>
                                </tr>
                                <tr>
                                    <td>黄瓜</td>
                                    <td>4</td>
                                    <td>斤</td>
                                </tr>
                                <tr>
                                    <td>胡萝卜</td>
                                    <td>2</td>
                                    <td>斤</td>
                                </tr>
                                <tr>
                                    <td>土豆</td>
                                    <td>8</td>
                                    <td>斤</td>
                                </tr>
                                <tr>
                                    <td>韭菜</td>
                                    <td>1</td>
                                    <td>把</td>
                                </tr>
                                <tr>
                                    <td>豆腐</td>
                                    <td>2</td>
                                    <td>盒</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 悬浮语音按钮 -->
                    <button class="voice-btn">
                        <svg style="width: 24px; height: 24px; fill: white;" viewBox="0 0 24 24">
                            <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
                        </svg>
                    </button>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分拣任务 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">分拣订单</h2>

                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">未分拣</button>
                        <button style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 16px; color: white; font-size: 14px;">已分拣</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">张先生</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024001</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 3件 | 优先级: 高
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">李女士</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024002</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 5件 | 优先级: 中
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">王先生</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024003</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 2件 | 优先级: 低
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">赵女士</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024004</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6); margin-bottom: 12px;">
                            商品: 4件 | 优先级: 高
                        </div>
                        <button class="glow-btn" style="font-size: 14px; padding: 8px 16px;">开始分拣</button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-sort"></use></svg>
                            <span>分拣</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分拣大厅 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">分拣大厅</h2>



                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">张先生</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024001 | 电话: 138****1234</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            地址: 北京市朝阳区xxx街道 | 商品: 3件
                        </div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">李女士</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024002 | 电话: 139****5678</div>
                            </div>
                            <span class="status-dot status-processing"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            地址: 上海市浦东新区xxx路 | 商品: 5件 | 分拣员: 小李
                        </div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">王先生</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024003 | 电话: 136****9012</div>
                            </div>
                            <span class="status-dot status-completed"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            地址: 广州市天河区xxx大道 | 商品: 2件 | 已完成
                        </div>
                    </div>

                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600;">赵女士</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单 #SO2024004 | 电话: 137****3456</div>
                            </div>
                            <span class="status-dot status-pending"></span>
                        </div>
                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                            地址: 深圳市南山区xxx街 | 商品: 4件
                        </div>
                    </div>



                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-sort"></use></svg>
                            <span>大厅</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人中心 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(45deg, #00d4ff, #ff0080); margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                            <svg class="icon" style="width: 40px; height: 40px;">
                                <use href="#icon-profile"></use>
                            </svg>
                        </div>
                        <h2 style="margin-bottom: 8px;">张三</h2>
                        <p style="color: rgba(255,255,255,0.6); font-size: 14px;">高级分拣员 | ID: 001</p>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px;">本月统计</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; text-align: center;">
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #00d4ff;">156</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">完成订单</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #30d158;">98.5%</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">准确率</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #ff9500;">12.3</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">平均用时(分)</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: #ff0080;">A+</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">绩效等级</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 style="margin-bottom: 16px;">系统设置</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>语音提示</span>
                            <div style="width: 40px; height: 20px; background: #00d4ff; border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span>震动反馈</span>
                            <div style="width: 40px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 10px; position: relative;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                            </div>
                        </div>
                        <button class="glow-btn" style="width: 100%; background: linear-gradient(45deg, #ff0080, #ff4081);">
                            退出登录
                        </button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 管理员参考明细页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">管理员明细</h2>

                    <!-- 财务概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">财务概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #30d158;">¥128,560</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月销售额</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #ff9500;">¥85,420</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月进货成本</div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #00d4ff;">¥43,140</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月利润</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 700; color: #ff0080;">¥12,350</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">应收欠款</div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存状况 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">库存状况</h3>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                            <span>蔬菜类</span>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 60px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                    <div style="width: 75%; height: 100%; background: #30d158; border-radius: 3px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #30d158;">75%</span>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                            <span>肉类</span>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 60px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                    <div style="width: 45%; height: 100%; background: #ff9500; border-radius: 3px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #ff9500;">45%</span>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>海鲜类</span>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div style="width: 60px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                    <div style="width: 20%; height: 100%; background: #ff0080; border-radius: 3px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #ff0080;">20%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 欠款明细 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">欠款明细</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: rgba(255,0,128,0.1); border-radius: 6px;">
                            <div>
                                <div style="font-weight: 600; font-size: 14px;">张记餐厅</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期15天</div>
                            </div>
                            <div style="color: #ff0080; font-weight: 600;">¥5,680</div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: rgba(255,0,128,0.1); border-radius: 6px;">
                            <div>
                                <div style="font-weight: 600; font-size: 14px;">李家小炒</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期8天</div>
                            </div>
                            <div style="color: #ff0080; font-weight: 600;">¥3,420</div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border-radius: 6px;">
                            <div>
                                <div style="font-weight: 600; font-size: 14px;">王氏火锅</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期3天</div>
                            </div>
                            <div style="color: #ff0080; font-weight: 600;">¥3,250</div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-login"></use></svg>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-scan"></use></svg>
                            <span>入库</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-order"></use></svg>
                            <span>订单</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-profile"></use></svg>
                            <span>管理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 横屏分拣页面 -->
        <div class="phone-frame landscape-page">
            <div class="sidebar">
                <div class="sidebar-item active">已分拣</div>
                <div class="sidebar-item">未分拣</div>
                <div class="sidebar-item">挂单</div>
                <div class="sidebar-item">大厅</div>
            </div>
            <div class="main-content">
                <!-- 标题栏带按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <button class="glow-btn" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(45deg, #ff9500, #ff6b00);">挂单</button>
                    <h2 class="page-title" style="margin: 0;">分拣作业 - 张先生订单</h2>
                    <button class="glow-btn" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(45deg, #30d158, #28a745);">分拣完成</button>
                </div>

                <div style="display: flex; gap: 16px; height: calc(100% - 80px);">
                    <!-- 中间分拣区域 -->
                    <div style="flex: 1;">
                        <div class="product-grid">
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">蘑菇</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">1箱</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 70%; height: 100%; background: #00d4ff; border-radius: 2px;"></div>
                                </div>
                            </div>

                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">西红柿</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">3斤</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #ff9500; border-radius: 2px;"></div>
                                </div>
                            </div>

                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">黄瓜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">4斤</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #ff9500; border-radius: 2px;"></div>
                                </div>
                            </div>

                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">胡萝卜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">2斤</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #ff9500; border-radius: 2px;"></div>
                                </div>
                            </div>

                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">土豆</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">8斤</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #ff9500; border-radius: 2px;"></div>
                                </div>
                            </div>

                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">韭菜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">1把</div>
                                <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">
                                    <div style="width: 0%; height: 100%; background: #ff9500; border-radius: 2px;"></div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- 完成列表 -->
                    <div style="width: 140px; background: rgba(48, 209, 88, 0.1); border: 1px solid rgba(48, 209, 88, 0.3); border-radius: 8px; padding: 12px; display: flex; flex-direction: column;">
                        <h4 style="color: #30d158; margin-bottom: 12px; font-size: 14px; text-align: center;">已完成列表</h4>
                        <div style="flex: 1; width: 100%; display: flex; flex-direction: column; gap: 8px; overflow-y: auto;">
                            <!-- 已完成的商品 -->
                            <div style="background: rgba(48, 209, 88, 0.2); border-radius: 6px; padding: 8px; text-align: center;">
                                <div style="font-size: 12px; font-weight: 600;">大白菜</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.7);">5斤</div>
                            </div>
                            <div style="background: rgba(48, 209, 88, 0.2); border-radius: 6px; padding: 8px; text-align: center;">
                                <div style="font-size: 12px; font-weight: 600;">小白菜</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.7);">6斤</div>
                            </div>
                            <div style="background: rgba(48, 209, 88, 0.2); border-radius: 6px; padding: 8px; text-align: center;">
                                <div style="font-size: 12px; font-weight: 600;">豆腐</div>
                                <div style="font-size: 10px; color: rgba(255,255,255,0.7);">2盒</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 会员中心 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">会员中心</h2>

                    <!-- 会员信息 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                            <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(45deg, #00d4ff, #ff0080); display: flex; align-items: center; justify-content: center;">
                                <svg class="icon" style="width: 30px; height: 30px;">
                                    <use href="#icon-member"></use>
                                </svg>
                            </div>
                            <div>
                                <h3 style="margin-bottom: 4px;">张记生鲜</h3>
                                <p style="color: rgba(255,255,255,0.6); font-size: 14px;">会员ID: VIP001 | 等级: 钻石会员</p>
                                <p style="color: rgba(255,255,255,0.6); font-size: 12px;">注册时间: 2023-01-15</p>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; text-align: center;">
                            <div>
                                <div style="font-size: 18px; font-weight: 700; color: #30d158;">¥128,560</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月营业额</div>
                            </div>
                            <div>
                                <div style="font-size: 18px; font-weight: 700; color: #00d4ff;">156</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月订单</div>
                            </div>
                            <div>
                                <div style="font-size: 18px; font-weight: 700; color: #ff9500;">8</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">子会员数</div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能模块 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">功能模块</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <button class="glow-btn" style="padding: 16px; background: linear-gradient(45deg, #30d158, #28a745); display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                <svg class="icon" style="width: 24px; height: 24px;">
                                    <use href="#icon-scan"></use>
                                </svg>
                                <span>入库管理</span>
                            </button>
                            <button class="glow-btn" style="padding: 16px; background: linear-gradient(45deg, #00d4ff, #0099cc); display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                <svg class="icon" style="width: 24px; height: 24px;">
                                    <use href="#icon-order"></use>
                                </svg>
                                <span>录单功能</span>
                            </button>
                            <button class="glow-btn" style="padding: 16px; background: linear-gradient(45deg, #ff9500, #ff6b00); display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                <svg class="icon" style="width: 24px; height: 24px;">
                                    <use href="#icon-sort"></use>
                                </svg>
                                <span>分拣系统</span>
                            </button>
                            <button class="glow-btn" style="padding: 16px; background: linear-gradient(45deg, #ff0080, #ff4081); display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                <svg class="icon" style="width: 24px; height: 24px;">
                                    <use href="#icon-finance"></use>
                                </svg>
                                <span>财务管理</span>
                            </button>
                            <button class="glow-btn" style="padding: 16px; background: linear-gradient(45deg, #8e44ad, #9b59b6); display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                <svg class="icon" style="width: 24px; height: 24px;">
                                    <use href="#icon-sale"></use>
                                </svg>
                                <span>快速售卖</span>
                            </button>
                            <button class="glow-btn" style="padding: 16px; background: linear-gradient(45deg, #34495e, #2c3e50); display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                <svg class="icon" style="width: 24px; height: 24px;">
                                    <use href="#icon-member"></use>
                                </svg>
                                <span>子会员管理</span>
                            </button>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中控台页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">中控台</h2>

                    <!-- 实时数据概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">今日实时数据</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div style="text-align: center; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: 700; color: #30d158;">156</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">今日订单总数</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(0,212,255,0.1); border: 1px solid #00d4ff; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: 700; color: #00d4ff;">128</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">已完成订单</div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div style="text-align: center; padding: 12px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: 700; color: #ff9500;">23</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣中订单</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(255,59,48,0.1); border: 1px solid #ff3b30; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: 700; color: #ff3b30;">5</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">待处理订单</div>
                            </div>
                        </div>
                    </div>

                    <!-- 在线人员状态 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">在线人员状态</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; text-align: center; margin-bottom: 16px;">
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #30d158;">8</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">在线员工</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #00d4ff;">5</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣中</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #ff9500;">3</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">空闲中</div>
                            </div>
                        </div>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">张三</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣员 | 正在处理订单#SO2024001</div>
                                </div>
                                <span class="status-dot status-processing"></span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">李四</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣员 | 正在处理订单#SO2024002</div>
                                </div>
                                <span class="status-dot status-processing"></span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600;">王五</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣员 | 空闲状态</div>
                                </div>
                                <span class="status-dot status-completed"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 今日销售额 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">今日销售概况</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span>销售额</span>
                            <span style="font-size: 18px; font-weight: 700; color: #30d158;">¥12,580</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span>成本</span>
                            <span style="font-size: 18px; font-weight: 700; color: #ff9500;">¥8,420</span>
                        </div>
                        <div style="border-top: 1px solid rgba(255,255,255,0.1); padding-top: 12px; display: flex; justify-content: space-between; align-items: center;">
                            <span>净利润</span>
                            <span style="font-size: 20px; font-weight: 700; color: #00d4ff;">¥4,160</span>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 子会员管理页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">子会员管理</h2>

                    <!-- 统计概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; text-align: center;">
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #30d158;">8</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">总子会员</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #00d4ff;">6</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">在线</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: 700; color: #ff9500;">2</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">离线</div>
                            </div>
                        </div>
                        <button class="glow-btn" style="width: 100%; margin-top: 16px; background: linear-gradient(45deg, #30d158, #28a745);">
                            + 添加子会员
                        </button>
                    </div>

                    <!-- 子会员列表 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px;">子会员列表</h3>
                        <div style="display: grid; gap: 12px;">
                            <div style="padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div>
                                        <div style="font-weight: 600; color: #30d158;">张三 (分拣员)</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">ID: SUB001 | 手机: 138****1234</div>
                                    </div>
                                    <span class="status-dot status-completed"></span>
                                </div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                                    权限: 分拣系统 | 本月完成: 156单 | 绩效: A+
                                </div>
                                <div style="display: flex; gap: 8px; margin-top: 8px;">
                                    <button style="padding: 4px 8px; background: rgba(0,212,255,0.2); border: 1px solid #00d4ff; border-radius: 4px; color: #00d4ff; font-size: 12px;">编辑</button>
                                    <button style="padding: 4px 8px; background: rgba(255,149,0,0.2); border: 1px solid #ff9500; border-radius: 4px; color: #ff9500; font-size: 12px;">权限</button>
                                </div>
                            </div>

                            <div style="padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div>
                                        <div style="font-weight: 600; color: #30d158;">李四 (录单员)</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">ID: SUB002 | 手机: 139****5678</div>
                                    </div>
                                    <span class="status-dot status-completed"></span>
                                </div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                                    权限: 录单功能 | 本月录单: 89单 | 绩效: A
                                </div>
                                <div style="display: flex; gap: 8px; margin-top: 8px;">
                                    <button style="padding: 4px 8px; background: rgba(0,212,255,0.2); border: 1px solid #00d4ff; border-radius: 4px; color: #00d4ff; font-size: 12px;">编辑</button>
                                    <button style="padding: 4px 8px; background: rgba(255,149,0,0.2); border: 1px solid #ff9500; border-radius: 4px; color: #ff9500; font-size: 12px;">权限</button>
                                </div>
                            </div>

                            <div style="padding: 12px; background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div>
                                        <div style="font-weight: 600;">王五 (入库员)</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">ID: SUB003 | 手机: 136****9012</div>
                                    </div>
                                    <span style="width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.3); display: inline-block;"></span>
                                </div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                                    权限: 入库管理 | 本月入库: 45批次 | 绩效: B+
                                </div>
                                <div style="display: flex; gap: 8px; margin-top: 8px;">
                                    <button style="padding: 4px 8px; background: rgba(0,212,255,0.2); border: 1px solid #00d4ff; border-radius: 4px; color: #00d4ff; font-size: 12px;">编辑</button>
                                    <button style="padding: 4px 8px; background: rgba(255,149,0,0.2); border: 1px solid #ff9500; border-radius: 4px; color: #ff9500; font-size: 12px;">权限</button>
                                </div>
                            </div>

                            <div style="padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <div>
                                        <div style="font-weight: 600; color: #30d158;">赵六 (财务)</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.6);">ID: SUB004 | 手机: 137****3456</div>
                                    </div>
                                    <span class="status-dot status-completed"></span>
                                </div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">
                                    权限: 财务管理 | 本月处理: 156笔 | 绩效: A+
                                </div>
                                <div style="display: flex; gap: 8px; margin-top: 8px;">
                                    <button style="padding: 4px 8px; background: rgba(0,212,255,0.2); border: 1px solid #00d4ff; border-radius: 4px; color: #00d4ff; font-size: 12px;">编辑</button>
                                    <button style="padding: 4px 8px; background: rgba(255,149,0,0.2); border: 1px solid #ff9500; border-radius: 4px; color: #ff9500; font-size: 12px;">权限</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>子会员</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加子会员页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">添加子会员</h2>

                    <!-- 基本信息 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">基本信息</h3>
                        <input type="text" class="input-field" placeholder="姓名" style="margin-bottom: 12px;">
                        <input type="text" class="input-field" placeholder="手机号码" style="margin-bottom: 12px;">
                        <input type="text" class="input-field" placeholder="身份证号" style="margin-bottom: 12px;">
                        <input type="password" class="input-field" placeholder="登录密码" style="margin-bottom: 0;">
                    </div>

                    <!-- 权限设置 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">功能权限设置</h3>
                        <div style="display: grid; gap: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">入库管理</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">进货单管理和入库操作</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #30d158; border-radius: 10px; position: relative;">
                                    <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(0,212,255,0.1); border: 1px solid #00d4ff; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600; color: #00d4ff;">录单功能</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">录入客户订单</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: #00d4ff; border-radius: 10px; position: relative;">
                                    <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff9500;">分拣系统</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">分拣员进行分拣操作</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 10px; position: relative;">
                                    <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">财务管理</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">收益统计、欠款管理</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 10px; position: relative;">
                                    <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(142,68,173,0.1); border: 1px solid #8e44ad; border-radius: 8px;">
                                <div>
                                    <div style="font-weight: 600; color: #8e44ad;">快速售卖</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">现货销售功能</div>
                                </div>
                                <div style="width: 40px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 10px; position: relative;">
                                    <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: flex; gap: 12px;">
                        <button class="glow-btn" style="flex: 1; background: linear-gradient(45deg, #30d158, #28a745);">
                            确认添加
                        </button>
                        <button style="flex: 1; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 12px; padding: 12px 24px; color: white; font-weight: 600;">
                            取消
                        </button>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>子会员</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-analytics"></use></svg>
                            <span>数据分析</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 财务管理页面 -->
        <div class="phone-frame">
            <div class="screen">
                <div class="page">
                    <h2 class="page-title">财务管理</h2>

                    <!-- 财务概览 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #30d158;">财务概览</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div style="text-align: center; padding: 12px; background: rgba(48,209,88,0.1); border: 1px solid #30d158; border-radius: 8px;">
                                <div style="font-size: 20px; font-weight: 700; color: #30d158;">¥128,560</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月销售额</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(255,149,0,0.1); border: 1px solid #ff9500; border-radius: 8px;">
                                <div style="font-size: 20px; font-weight: 700; color: #ff9500;">¥85,420</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月成本</div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div style="text-align: center; padding: 12px; background: rgba(0,212,255,0.1); border: 1px solid #00d4ff; border-radius: 8px;">
                                <div style="font-size: 20px; font-weight: 700; color: #00d4ff;">¥43,140</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">本月利润</div>
                            </div>
                            <div style="text-align: center; padding: 12px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 8px;">
                                <div style="font-size: 20px; font-weight: 700; color: #ff0080;">¥12,350</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.6);">应收欠款</div>
                            </div>
                        </div>
                    </div>

                    <!-- 今日收支明细 -->
                    <div class="card" style="margin-bottom: 16px;">
                        <h3 style="margin-bottom: 16px; color: #00d4ff;">今日收支明细</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(48,209,88,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #30d158;">销售收入</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">订单156笔</div>
                                </div>
                                <div style="color: #30d158; font-weight: 600;">+¥4,280</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,59,48,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff3b30;">进货支出</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">采购3批次</div>
                                </div>
                                <div style="color: #ff3b30; font-weight: 600;">-¥2,850</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,59,48,0.1); border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff3b30;">运营费用</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">人工+水电</div>
                                </div>
                                <div style="color: #ff3b30; font-weight: 600;">-¥680</div>
                            </div>
                        </div>
                        <div style="border-top: 1px solid rgba(255,255,255,0.1); margin-top: 12px; padding-top: 12px; display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 600;">今日净利润</div>
                            <div style="color: #30d158; font-weight: 700; font-size: 18px;">+¥750</div>
                        </div>
                    </div>

                    <!-- 欠款管理 -->
                    <div class="card">
                        <h3 style="margin-bottom: 16px; color: #ff0080;">欠款管理</h3>
                        <div style="display: grid; gap: 8px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">张记餐厅</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期15天 | 电话: 138****1234</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ff0080; font-weight: 600;">¥5,680</div>
                                    <button style="padding: 2px 6px; background: rgba(255,0,128,0.2); border: 1px solid #ff0080; border-radius: 4px; color: #ff0080; font-size: 10px; margin-top: 4px;">催收</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">李家小炒</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期8天 | 电话: 139****5678</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ff0080; font-weight: 600;">¥3,420</div>
                                    <button style="padding: 2px 6px; background: rgba(255,0,128,0.2); border: 1px solid #ff0080; border-radius: 4px; color: #ff0080; font-size: 10px; margin-top: 4px;">催收</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: rgba(255,0,128,0.1); border: 1px solid #ff0080; border-radius: 6px;">
                                <div>
                                    <div style="font-weight: 600; color: #ff0080;">王氏火锅</div>
                                    <div style="font-size: 12px; color: rgba(255,255,255,0.6);">逾期3天 | 电话: 136****9012</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ff0080; font-weight: 600;">¥3,250</div>
                                    <button style="padding: 2px 6px; background: rgba(255,0,128,0.2); border: 1px solid #ff0080; border-radius: 4px; color: #ff0080; font-size: 10px; margin-top: 4px;">催收</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="nav-bar">
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-dashboard"></use></svg>
                            <span>中控台</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-member"></use></svg>
                            <span>会员中心</span>
                        </div>
                        <div class="nav-item active">
                            <svg class="icon"><use href="#icon-finance"></use></svg>
                            <span>财务</span>
                        </div>
                        <div class="nav-item">
                            <svg class="icon"><use href="#icon-settings"></use></svg>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速售卖页面 - 横屏界面 -->
        <div class="phone-frame landscape-page">
            <div class="sidebar">
                <div class="sidebar-item active">蔬菜</div>
                <div class="sidebar-item">肉类</div>
                <div class="sidebar-item">大荤</div>
            </div>
            <div class="main-content">
                <!-- 标题栏带按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <button class="glow-btn" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(45deg, #ff9500, #ff6b00);">挂单</button>
                    <h2 class="page-title" style="margin: 0;">快速售卖 - 张记生鲜</h2>
                    <button class="glow-btn" style="padding: 8px 16px; font-size: 14px; background: linear-gradient(45deg, #30d158, #28a745);">分拣完成</button>
                </div>

                <div style="display: flex; gap: 16px; height: calc(100% - 80px);">
                    <!-- 中间商品区域 -->
                    <div style="flex: 1;">
                        <div class="product-grid">
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">蘑菇</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">1箱</div>
                            </div>
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">西红柿</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">3斤</div>
                            </div>
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">黄瓜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">4斤</div>
                            </div>
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">胡萝卜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">2斤</div>
                            </div>
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">土豆</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">8斤</div>
                            </div>
                            <div class="product-card">
                                <h3 style="margin-bottom: 12px;">韭菜</h3>
                                <div style="font-size: 20px; font-weight: 700; margin-bottom: 16px;">1把</div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧操作面板 -->
                    <div style="width: 200px;">
                        <div style="background: rgba(48, 209, 88, 0.1); border: 1px solid #30d158; border-radius: 12px; padding: 16px; margin-bottom: 16px;">
                            <h4 style="color: #30d158; margin-bottom: 12px;">已完成售卖</h4>
                            <button class="glow-btn" style="width: 100%; margin-bottom: 8px; background: linear-gradient(45deg, #30d158, #28a745); padding: 16px 12px;">
                                大白菜<br>5斤
                            </button>
                            <button class="glow-btn" style="width: 100%; margin-bottom: 8px; background: linear-gradient(45deg, #30d158, #28a745); padding: 12px;">
                                小白菜<br>3斤
                            </button>
                            <button class="glow-btn" style="width: 100%; background: linear-gradient(45deg, #30d158, #28a745); padding: 12px;">
                                豆苗<br>1斤
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
